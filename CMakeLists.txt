cmake_minimum_required(VERSION 3.25)

project(NExT 
    VERSION 0.1.0
    DESCRIPTION "NExT Language - Nostalgic Extensible Templated language for retro computing"
    LANGUAGES C CXX
)

# Set C++23 standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set C11 standard for ca65 compatibility
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build options
option(NEXT_BUILD_TESTS "Build NExT tests" ON)
option(NEXT_BUILD_EXAMPLES "Build NExT examples" ON)
option(NEXT_BUILD_SHARED_LIBS "Build shared libraries" OFF)

# Global compiler settings
if(MSVC)
    add_compile_options(/W4 /permissive-)
    # Enable C++23 modules support
    add_compile_options(/experimental:module)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    # Enable C++23 modules support for GCC/Clang
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        add_compile_options(-fmodules-ts)
    elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
        add_compile_options(-fmodules)
    endif()
endif()

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Global include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src)

# Add third-party dependencies
add_subdirectory(3rdparty)

# Add main source directories
add_subdirectory(src)

# Add examples if requested
if(NEXT_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Enable testing
if(NEXT_BUILD_TESTS)
    enable_testing()
    include(CTest)
endif()

# Installation configuration
include(GNUInstallDirs)

# Export targets for find_package support
install(EXPORT NExTTargets
    FILE NExTTargets.cmake
    NAMESPACE NExT::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/NExT
)

# Create config file
include(CMakePackageConfigHelpers)
configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/NExTConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/NExTConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/NExT
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/NExTConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/NExTConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/NExTConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/NExT
)

# Print configuration summary
message(STATUS "")
message(STATUS "NExT Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build tests: ${NEXT_BUILD_TESTS}")
message(STATUS "  Build examples: ${NEXT_BUILD_EXAMPLES}")
message(STATUS "  Build shared libs: ${NEXT_BUILD_SHARED_LIBS}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
