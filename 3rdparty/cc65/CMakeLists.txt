# CMake configuration for building ca65 as a library
# This builds only the ca65 assembler core without the CLI interface

cmake_minimum_required(VERSION 3.25)

# Check if cc65 source exists
if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/src/ca65/main.c")
    message(FATAL_ERROR 
        "cc65 source not found. Please run:\n"
        "  git submodule update --init --recursive\n"
        "or download cc65 source to 3rdparty/cc65/")
endif()

# Set source directories
set(CA65_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src/ca65")
set(COMMON_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src/common")

# ca65 source files (excluding main.c)
set(CA65_SOURCES
    ${CA65_SRC_DIR}/anonname.c
    ${CA65_SRC_DIR}/asserts.c
    ${CA65_SRC_DIR}/condasm.c
    ${CA65_SRC_DIR}/dbginfo.c
    ${CA65_SRC_DIR}/ea65.c
    ${CA65_SRC_DIR}/easw16.c
    ${CA65_SRC_DIR}/enum.c
    ${CA65_SRC_DIR}/error.c
    ${CA65_SRC_DIR}/expr.c
    ${CA65_SRC_DIR}/feature.c
    ${CA65_SRC_DIR}/filetab.c
    ${CA65_SRC_DIR}/fragment.c
    ${CA65_SRC_DIR}/global.c
    ${CA65_SRC_DIR}/incpath.c
    ${CA65_SRC_DIR}/instr.c
    ${CA65_SRC_DIR}/istack.c
    ${CA65_SRC_DIR}/lineinfo.c
    ${CA65_SRC_DIR}/listing.c
    ${CA65_SRC_DIR}/macro.c
    ${CA65_SRC_DIR}/nexttok.c
    ${CA65_SRC_DIR}/objcode.c
    ${CA65_SRC_DIR}/objfile.c
    ${CA65_SRC_DIR}/options.c
    ${CA65_SRC_DIR}/pseudo.c
    ${CA65_SRC_DIR}/repeat.c
    ${CA65_SRC_DIR}/scanner.c
    ${CA65_SRC_DIR}/segdef.c
    ${CA65_SRC_DIR}/segment.c
    ${CA65_SRC_DIR}/sizeof.c
    ${CA65_SRC_DIR}/span.c
    ${CA65_SRC_DIR}/spool.c
    ${CA65_SRC_DIR}/struct.c
    ${CA65_SRC_DIR}/studyexpr.c
    ${CA65_SRC_DIR}/symbol.c
    ${CA65_SRC_DIR}/symentry.c
    ${CA65_SRC_DIR}/symtab.c
    ${CA65_SRC_DIR}/token.c
    ${CA65_SRC_DIR}/toklist.c
    ${CA65_SRC_DIR}/ulabel.c
)

# Common library source files
set(COMMON_SOURCES
    ${COMMON_SRC_DIR}/abend.c
    ${COMMON_SRC_DIR}/addrsize.c
    ${COMMON_SRC_DIR}/alignment.c
    ${COMMON_SRC_DIR}/bitops.c
    ${COMMON_SRC_DIR}/chartype.c
    ${COMMON_SRC_DIR}/check.c
    ${COMMON_SRC_DIR}/cmdline.c
    ${COMMON_SRC_DIR}/coll.c
    ${COMMON_SRC_DIR}/cpu.c
    ${COMMON_SRC_DIR}/debugflag.c
    ${COMMON_SRC_DIR}/exprdefs.c
    ${COMMON_SRC_DIR}/filepos.c
    ${COMMON_SRC_DIR}/filetype.c
    ${COMMON_SRC_DIR}/fname.c
    ${COMMON_SRC_DIR}/hashstr.c
    ${COMMON_SRC_DIR}/hashtab.c
    ${COMMON_SRC_DIR}/intstack.c
    ${COMMON_SRC_DIR}/mmodel.c
    ${COMMON_SRC_DIR}/print.c
    ${COMMON_SRC_DIR}/searchpath.c
    ${COMMON_SRC_DIR}/segnames.c
    ${COMMON_SRC_DIR}/strbuf.c
    ${COMMON_SRC_DIR}/strpool.c
    ${COMMON_SRC_DIR}/strstack.c
    ${COMMON_SRC_DIR}/target.c
    ${COMMON_SRC_DIR}/tgttrans.c
    ${COMMON_SRC_DIR}/version.c
    ${COMMON_SRC_DIR}/xmalloc.c
    ${COMMON_SRC_DIR}/xsprintf.c
)

# Create ca65 library
add_library(ca65_core STATIC ${CA65_SOURCES} ${COMMON_SOURCES})

# Set include directories
target_include_directories(ca65_core PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CA65_SRC_DIR}
    ${COMMON_SRC_DIR}
)

# Compiler definitions for ca65
target_compile_definitions(ca65_core PRIVATE
    # Disable some features we don't need for library usage
    $<$<PLATFORM_ID:Windows>:_CRT_SECURE_NO_WARNINGS>
)

# Set library properties
set_target_properties(ca65_core PROPERTIES
    OUTPUT_NAME "ca65"
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    POSITION_INDEPENDENT_CODE ON
)

# Platform-specific settings
if(WIN32)
    target_compile_definitions(ca65_core PRIVATE WIN32_LEAN_AND_MEAN)
endif()

# Export the target
add_library(NExT::ca65 ALIAS ca65_core)

# Installation
install(TARGETS ca65_core
    EXPORT NExTTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# Install headers that might be needed by the wrapper
install(DIRECTORY ${CA65_SRC_DIR}/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/ca65
    FILES_MATCHING PATTERN "*.h"
)

install(DIRECTORY ${COMMON_SRC_DIR}/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/common
    FILES_MATCHING PATTERN "*.h"
)
