# Tests for NExT 6502 Assembler Backend

cmake_minimum_required(VERSION 3.25)

# Find required packages for testing
find_package(GTest QUIET)

if(NOT GTest_FOUND)
    # Download and build GoogleTest if not found
    include(FetchContent)
    FetchContent_Declare(
        googletest
        URL https://github.com/google/googletest/archive/03597a01ee50ed33e9fd7188ec8e5902e4ec0908.zip
    )
    
    # For Windows: Prevent overriding the parent project's compiler/linker settings
    set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
    FetchContent_MakeAvailable(googletest)
endif()

# Test source files
set(TEST_SOURCES
    TestConfig.cpp
    TestAssembler.cpp
    TestTypes.cpp
)

# Create test executable
add_executable(next_6502_tests ${TEST_SOURCES})

# Set C++23 standard for tests
set_target_properties(next_6502_tests PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
)

# Link with our library and GoogleTest
target_link_libraries(next_6502_tests
    PRIVATE
        NExT::Assembler::6502
        gtest_main
        gtest
)

# Include directories
target_include_directories(next_6502_tests
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/../Include
)

# Compiler-specific settings for tests
if(MSVC)
    target_compile_options(next_6502_tests PRIVATE /W4)
else()
    target_compile_options(next_6502_tests PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Register tests with CTest
include(GoogleTest)
gtest_discover_tests(next_6502_tests)

# Add custom test target
add_custom_target(test_6502
    COMMAND next_6502_tests
    DEPENDS next_6502_tests
    COMMENT "Running NExT 6502 Assembler tests"
)
