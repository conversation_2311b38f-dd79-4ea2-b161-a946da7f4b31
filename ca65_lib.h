/*****************************************************************************/
/*                                                                           */
/*                                ca65_lib.h                                */
/*                                                                           */
/*                    Library interface for ca65 assembler                  */
/*                                                                           */
/*                                                                           */
/*                                                                           */
/* This is a compatibility layer that wraps ca65's core functionality       */
/* to provide a clean library interface for the NExT language project.     */
/*                                                                           */
/*****************************************************************************/

#ifndef CA65_LIB_H
#define CA65_LIB_H

#include <stddef.h>
#include <stdint.h>

/*****************************************************************************/
/*                                   Data                                   */
/*****************************************************************************/

/* Opaque handle for assembler context */
typedef struct Ca65Context Ca65Context;

/* CPU types supported by ca65 */
typedef enum {
    CA65_CPU_6502,
    CA65_CPU_6502X,
    CA65_CPU_6502DTV,
    CA65_CPU_65SC02,
    CA65_CPU_65C02,
    CA65_CPU_65816,
    CA65_CPU_HUC6280,
    CA65_CPU_4510,
    CA65_CPU_45GS02,
    CA65_CPU_M740,
    CA65_CPU_SWEET16
} Ca65CpuType;

/* Target systems */
typedef enum {
    CA65_TARGET_NONE,
    CA65_TARGET_ATARI2600,
    CA65_TARGET_ATARI5200,
    CA65_TARGET_ATARI7800,
    CA65_TARGET_ATARI,
    CA65_TARGET_ATARIXL,
    CA65_TARGET_C16,
    CA65_TARGET_C64,
    CA65_TARGET_C65,
    CA65_TARGET_VIC20,
    CA65_TARGET_C128,
    CA65_TARGET_PLUS4,
    CA65_TARGET_CBM510,
    CA65_TARGET_CBM610,
    CA65_TARGET_PET,
    CA65_TARGET_BBC,
    CA65_TARGET_APPLE2,
    CA65_TARGET_APPLE2ENH,
    CA65_TARGET_NES,
    CA65_TARGET_SUPERVISION,
    CA65_TARGET_LYNX,
    CA65_TARGET_SIM6502,
    CA65_TARGET_SIM65C02,
    CA65_TARGET_OSIC1P,
    CA65_TARGET_PCENGINE,
    CA65_TARGET_CX16,
    CA65_TARGET_SYM1,
    CA65_TARGET_MEGA65,
    CA65_TARGET_KIM1,
    CA65_TARGET_RP6502,
    CA65_TARGET_AGAT
} Ca65TargetSystem;

/* Memory models */
typedef enum {
    CA65_MMODEL_NEAR,
    CA65_MMODEL_FAR,
    CA65_MMODEL_HUGE
} Ca65MemoryModel;

/* Assembly result structure */
typedef struct {
    uint8_t*    ObjectData;     /* Generated object file data */
    size_t      ObjectSize;     /* Size of object data */
    int         ErrorCount;     /* Number of errors encountered */
    int         WarningCount;   /* Number of warnings */
    char*       ErrorMessages; /* Concatenated error messages */
} Ca65Result;

/* Configuration structure */
typedef struct {
    Ca65CpuType         CPU;
    Ca65TargetSystem    Target;
    Ca65MemoryModel     MemoryModel;
    int                 DebugInfo;      /* Include debug information */
    int                 SmartMode;      /* Enable smart mode */
    int                 IgnoreCase;     /* Case insensitive symbols */
    int                 AutoImport;     /* Auto-import undefined symbols */
    int                 RelaxChecks;    /* Relax some error checks */
    int                 WarningsAsErrors; /* Treat warnings as errors */
    const char**        IncludePaths;   /* NULL-terminated array of include paths */
    const char**        Defines;        /* NULL-terminated array of "NAME=VALUE" defines */
} Ca65Config;

/*****************************************************************************/
/*                                   Code                                   */
/*****************************************************************************/

/* Initialize the ca65 library */
int ca65_init(void);

/* Cleanup the ca65 library */
void ca65_cleanup(void);

/* Create a new assembler context with default configuration */
Ca65Context* ca65_context_new(void);

/* Create a new assembler context with specific configuration */
Ca65Context* ca65_context_new_with_config(const Ca65Config* config);

/* Free an assembler context */
void ca65_context_free(Ca65Context* ctx);

/* Set CPU type for the context */
int ca65_context_set_cpu(Ca65Context* ctx, Ca65CpuType cpu);

/* Set target system for the context */
int ca65_context_set_target(Ca65Context* ctx, Ca65TargetSystem target);

/* Set memory model for the context */
int ca65_context_set_memory_model(Ca65Context* ctx, Ca65MemoryModel model);

/* Add an include path */
int ca65_context_add_include_path(Ca65Context* ctx, const char* path);

/* Define a symbol */
int ca65_context_define_symbol(Ca65Context* ctx, const char* name, long value);

/* Assemble source code from string */
Ca65Result* ca65_assemble_string(Ca65Context* ctx, const char* source);

/* Assemble source code from memory buffer */
Ca65Result* ca65_assemble_buffer(Ca65Context* ctx, const char* buffer, size_t length);

/* Free assembly result */
void ca65_result_free(Ca65Result* result);

/* Get error message by index */
const char* ca65_result_get_error(Ca65Result* result, int index);

/* Get warning message by index */
const char* ca65_result_get_warning(Ca65Result* result, int index);

/* Utility function to get default configuration for a target */
Ca65Config ca65_get_default_config(Ca65TargetSystem target);

/* Utility function to validate configuration */
int ca65_validate_config(const Ca65Config* config);

#endif /* CA65_LIB_H */
