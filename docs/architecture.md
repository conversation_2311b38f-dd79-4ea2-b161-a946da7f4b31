# NExT 6502 Assembler Architecture

## Overview

The NExT 6502 Assembler backend provides a modern C++23 interface to the proven ca65 assembler from the cc65 suite. This design allows us to leverage ca65's mature 6502/65816 support while providing a clean, type-safe API for the NExT language project.

## Architecture Layers

### 1. C++23 Public API Layer
**Location**: `src/Assembler/6502/Include/NExT/Assembler/6502/`

This layer provides the main interface that NExT language users will interact with:

- **Types.hpp**: Core type definitions, enums, and concepts
- **Config.hpp**: Configuration classes and builder patterns
- **Assembler.hpp**: Main assembler class and factory functions

**Key Features**:
- Modern C++23 features (concepts, std::expected, ranges)
- RAII resource management
- Type-safe configuration
- Builder pattern for complex setups
- Comprehensive error handling

### 2. C++ Implementation Layer
**Location**: `src/Assembler/6502/Source/`

This layer implements the C++ API and manages the interface to the C compatibility layer:

- **Assembler.cpp**: Main assembler implementation
- **Config.cpp**: Configuration management
- **Ca65Wrapper.cpp**: C++ wrapper around C compatibility layer

### 3. C Compatibility Layer
**Location**: `src/Assembler/6502/Source/Ca65Wrapper.h`

This layer provides a clean C interface that wraps ca65's internal functions:

- Memory-based input/output (no file I/O required)
- Context management for multiple assembler instances
- Error and warning collection
- Configuration translation between C++ and ca65 types

### 4. ca65 Core Library
**Location**: `3rdparty/cc65/src/ca65/` and `3rdparty/cc65/src/common/`

The unmodified ca65 assembler core, compiled as a static library:

- All ca65 source files except `main.c`
- Common utility functions from cc65
- Proven 6502/65816 instruction encoding
- Object file generation

## Design Principles

### 1. **Zero ca65 Modifications**
- ca65 source code remains completely unmodified
- Preserves ability to update from upstream cc65 repository
- Reduces maintenance burden and testing requirements

### 2. **Architecture Separation**
- Clean namespace separation: `NExT::Assembler::M6502`
- Allows future addition of other architectures (Z80, 68000, etc.)
- Each architecture backend is self-contained

### 3. **Modern C++ Idioms**
- RAII for automatic resource management
- Concepts for type safety and better error messages
- `std::expected` for composable error handling
- Builder pattern for fluent configuration
- Move semantics for efficient object handling

### 4. **Memory Safety**
- No raw pointers in public API
- Automatic cleanup of ca65 resources
- Exception safety guarantees
- Bounds checking where appropriate

## Data Flow

```
User Code
    ↓ (C++23 API)
Assembler::Assemble()
    ↓ (C++ Implementation)
Ca65Wrapper functions
    ↓ (C Compatibility Layer)
ca65 core functions
    ↓ (Assembly Processing)
Object Code + Errors/Warnings
    ↓ (Result Processing)
AssemblyResult (std::expected)
    ↓ (Return to User)
User Code
```

## Error Handling Strategy

### 1. **Layered Error Handling**
- ca65 errors collected at C layer
- Translated to C++ exceptions or std::expected
- Rich error information preserved through all layers

### 2. **Error Types**
- **Assembly Errors**: Syntax errors, undefined symbols, etc.
- **Configuration Errors**: Invalid CPU/target combinations
- **System Errors**: Memory allocation failures, file I/O errors

### 3. **Error Propagation**
- C layer: Return codes and error collection
- C++ layer: std::expected for composable error handling
- User layer: Choice between exceptions or error checking

## Memory Management

### 1. **Context Isolation**
- Each `Assembler` instance has its own ca65 context
- No global state shared between instances
- Thread-safe by design (no shared mutable state)

### 2. **Resource Cleanup**
- RAII ensures automatic cleanup of ca65 resources
- Custom deleters for ca65 context and results
- Exception-safe resource management

### 3. **Memory Efficiency**
- Move semantics for large object code buffers
- Minimal copying of assembly source code
- Efficient string handling with string_view

## Configuration Management

### 1. **Type-Safe Configuration**
- Strong enums for CPU types and target systems
- Compile-time validation where possible
- Builder pattern for complex configurations

### 2. **Default Configurations**
- Predefined configurations for common systems
- Factory functions for quick setup
- Validation of CPU/target compatibility

### 3. **Runtime Configuration**
- Symbol definitions
- Include path management
- Feature flag control

## Testing Strategy

### 1. **Unit Tests**
- Test each layer independently
- Mock interfaces for isolation
- Comprehensive coverage of error conditions

### 2. **Integration Tests**
- End-to-end assembly of real 6502 programs
- Comparison with standalone ca65 output
- Performance benchmarking

### 3. **Example Programs**
- Demonstrate API usage patterns
- Serve as integration tests
- Document best practices

## Future Extensibility

### 1. **Additional Architectures**
- `NExT::Assembler::Z80` for Z80-based systems
- `NExT::Assembler::M68K` for 68000-based systems
- Common interface patterns across architectures

### 2. **Advanced Features**
- Template system integration
- Incremental assembly
- Symbol table introspection
- Custom segment handling

### 3. **Performance Optimizations**
- Assembly result caching
- Parallel template instantiation
- Memory pool allocation

This architecture provides a solid foundation for the NExT language while maintaining flexibility for future enhancements and additional target architectures.
