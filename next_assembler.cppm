/*****************************************************************************/
/*                                                                           */
/*                            next_assembler.cppm                           */
/*                                                                           */
/*                    Modern C++23 Assembler Interface Module               */
/*                                                                           */
/*                                                                           */
/*                                                                           */
/* This module provides a modern C++23 interface for the NExT language      */
/* assembler, wrapping the ca65 compatibility layer with RAII, concepts,    */
/* ranges, and other modern C++ features.                                   */
/*                                                                           */
/*****************************************************************************/

module;

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <span>
#include <expected>
#include <ranges>
#include <concepts>
#include <format>
#include <source_location>

// C compatibility layer
extern "C" {
#include "ca65_lib.h"
}

export module next.assembler;

export namespace Next::Assembler {

/*****************************************************************************/
/*                                 Concepts                                  */
/*****************************************************************************/

template<typename T>
concept StringLike = std::convertible_to<T, std::string_view>;

template<typename T>
concept AssemblySource = requires(T t) {
    { t.Data() } -> std::convertible_to<const char*>;
    { t.Size() } -> std::convertible_to<std::size_t>;
};

template<typename T>
concept ConfigurableTarget = requires(T t) {
    { t.Cpu() } -> std::same_as<CpuType>;
    { t.Target() } -> std::same_as<TargetSystem>;
    { t.MemoryModel() } -> std::same_as<MemoryModel>;
};

/*****************************************************************************/
/*                                   Enums                                  */
/*****************************************************************************/

enum class CpuType {
    MOS6502,
    MOS6502X,
    MOS6502DTV,
    WDC65SC02,
    WDC65C02,
    WDC65816,
    HuC6280,
    CSG4510,
    CSG45GS02,
    M740,
    Sweet16
};

enum class TargetSystem {
    None,
    Atari2600, Atari5200, Atari7800, Atari, AtariXL,
    Commodore16, Commodore64, Commodore65, VIC20, Commodore128,
    Plus4, CBM510, CBM610, PET, BBC,
    Apple2, Apple2Enhanced, NES, Supervision, Lynx,
    Sim6502, Sim65C02, OSI_C1P, PCEngine, CommanderX16,
    SYM1, MEGA65, KIM1, RP6502, Agat
};

enum class MemoryModel {
    Near, Far, Huge
};

/*****************************************************************************/
/*                              Error Handling                              */
/*****************************************************************************/

struct AssemblyError {
    std::string Message;
    std::source_location Location;
    int LineNumber{0};
    int Column{0};

    AssemblyError(std::string msg,
                  std::source_location loc = std::source_location::current())
        : Message(std::move(msg)), Location(loc) {}
};

struct AssemblyWarning {
    std::string Message;
    int LineNumber{0};
    int Column{0};

    AssemblyWarning(std::string msg) : Message(std::move(msg)) {}
};

using AssemblyResult = std::expected<std::vector<std::byte>, AssemblyError>;

/*****************************************************************************/
/*                              Configuration                               */
/*****************************************************************************/

class AssemblerConfig {
public:
    AssemblerConfig() = default;

    constexpr AssemblerConfig& Cpu(CpuType type) noexcept {
        cpu_ = type;
        return *this;
    }

    constexpr AssemblerConfig& Target(TargetSystem sys) noexcept {
        target_ = sys;
        return *this;
    }

    constexpr AssemblerConfig& MemoryModel(MemoryModel model) noexcept {
        memoryModel_ = model;
        return *this;
    }

    constexpr AssemblerConfig& DebugInfo(bool enable = true) noexcept {
        debugInfo_ = enable;
        return *this;
    }

    constexpr AssemblerConfig& SmartMode(bool enable = true) noexcept {
        smartMode_ = enable;
        return *this;
    }

    constexpr AssemblerConfig& IgnoreCase(bool enable = true) noexcept {
        ignoreCase_ = enable;
        return *this;
    }

    constexpr AssemblerConfig& AutoImport(bool enable = true) noexcept {
        autoImport_ = enable;
        return *this;
    }

    constexpr AssemblerConfig& WarningsAsErrors(bool enable = true) noexcept {
        warningsAsErrors_ = enable;
        return *this;
    }

    AssemblerConfig& IncludePath(std::string_view path) {
        includePaths_.emplace_back(path);
        return *this;
    }

    template<std::ranges::range R>
    AssemblerConfig& IncludePaths(R&& paths) {
        for (auto&& path : paths) {
            includePaths_.emplace_back(path);
        }
        return *this;
    }

    AssemblerConfig& Define(std::string_view name, std::integral auto value) {
        defines_.emplace_back(std::format("{}={}", name, value));
        return *this;
    }

    AssemblerConfig& Define(std::string_view name, std::string_view value) {
        defines_.emplace_back(std::format("{}={}", name, value));
        return *this;
    }

    // Getters
    constexpr CpuType Cpu() const noexcept { return cpu_; }
    constexpr TargetSystem Target() const noexcept { return target_; }
    constexpr MemoryModel MemoryModel() const noexcept { return memoryModel_; }
    constexpr bool DebugInfo() const noexcept { return debugInfo_; }
    constexpr bool SmartMode() const noexcept { return smartMode_; }
    constexpr bool IgnoreCase() const noexcept { return ignoreCase_; }
    constexpr bool AutoImport() const noexcept { return autoImport_; }
    constexpr bool WarningsAsErrors() const noexcept { return warningsAsErrors_; }

    const std::vector<std::string>& IncludePaths() const noexcept {
        return includePaths_;
    }

    const std::vector<std::string>& Defines() const noexcept {
        return defines_;
    }

private:
    CpuType cpu_{CpuType::MOS6502};
    TargetSystem target_{TargetSystem::None};
    MemoryModel memoryModel_{MemoryModel::Near};
    bool debugInfo_{false};
    bool smartMode_{false};
    bool ignoreCase_{false};
    bool autoImport_{false};
    bool warningsAsErrors_{false};
    std::vector<std::string> includePaths_;
    std::vector<std::string> defines_;
};

/*****************************************************************************/
/*                              Assembly Result                             */
/*****************************************************************************/

class AssemblyOutput {
public:
    AssemblyOutput(std::vector<std::byte> objectCode,
                   std::vector<AssemblyWarning> warnings = {})
        : objectCode_(std::move(objectCode))
        , warnings_(std::move(warnings)) {}

    const std::vector<std::byte>& ObjectCode() const noexcept {
        return objectCode_;
    }

    std::span<const std::byte> ObjectSpan() const noexcept {
        return objectCode_;
    }

    const std::vector<AssemblyWarning>& Warnings() const noexcept {
        return warnings_;
    }

    bool HasWarnings() const noexcept {
        return !warnings_.empty();
    }

    std::size_t Size() const noexcept {
        return objectCode_.size();
    }

    bool Empty() const noexcept {
        return objectCode_.empty();
    }

private:
    std::vector<std::byte> objectCode_;
    std::vector<AssemblyWarning> warnings_;
};

/*****************************************************************************/
/*                              Main Interface                              */
/*****************************************************************************/

class Assembler {
public:
    explicit Assembler(AssemblerConfig config = {});
    ~Assembler();

    // Non-copyable, movable
    Assembler(const Assembler&) = delete;
    Assembler& operator=(const Assembler&) = delete;
    Assembler(Assembler&&) noexcept;
    Assembler& operator=(Assembler&&) noexcept;

    // Assembly functions
    AssemblyResult Assemble(StringLike auto&& source) {
        return AssembleImpl(std::string_view{source});
    }

    template<AssemblySource T>
    AssemblyResult Assemble(const T& source) {
        return AssembleBuffer(source.Data(), source.Size());
    }

    AssemblyResult AssembleFile(std::string_view filename);

    // Configuration
    void Reconfigure(AssemblerConfig newConfig);
    const AssemblerConfig& Config() const noexcept { return config_; }

    // Symbol management
    void DefineSymbol(std::string_view name, std::integral auto value);
    void AddIncludePath(std::string_view path);

private:
    AssemblyResult AssembleImpl(std::string_view source);
    AssemblyResult AssembleBuffer(const char* data, std::size_t size);

    AssemblerConfig config_;
    std::unique_ptr<Ca65Context, void(*)(Ca65Context*)> context_;

    static Ca65CpuType ConvertCpuType(CpuType cpu) noexcept;
    static Ca65TargetSystem ConvertTargetSystem(TargetSystem target) noexcept;
    static Ca65MemoryModel ConvertMemoryModel(MemoryModel model) noexcept;
};

/*****************************************************************************/
/*                              Factory Functions                           */
/*****************************************************************************/

// Factory functions for common configurations
Assembler MakeC64Assembler();
Assembler MakeNesAssembler();
Assembler MakeApple2Assembler();
Assembler MakeGeneric6502Assembler();

// Builder pattern for complex configurations
class AssemblerBuilder {
public:
    AssemblerBuilder& ForTarget(TargetSystem target);
    AssemblerBuilder& WithCpu(CpuType cpu);
    AssemblerBuilder& WithDebugInfo();
    AssemblerBuilder& WithSmartMode();
    AssemblerBuilder& CaseInsensitive();
    AssemblerBuilder& IncludePath(std::string_view path);
    AssemblerBuilder& Define(std::string_view name, auto value);

    Assembler Build();

private:
    AssemblerConfig config_;
};

} // namespace Next::Assembler
