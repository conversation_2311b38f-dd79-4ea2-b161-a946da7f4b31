/*****************************************************************************/
/*                                                                           */
/*                            next_assembler.cpp                            */
/*                                                                           */
/*                    Modern C++23 Assembler Implementation                 */
/*                                                                           */
/*****************************************************************************/

module next.assembler;

import <memory>;
import <stdexcept>;
import <algorithm>;
import <ranges>;

namespace next::assembler {

/*****************************************************************************/
/*                              Type Conversions                            */
/*****************************************************************************/

Ca65CpuType Assembler::convert_cpu_type(CpuType cpu) noexcept {
    switch (cpu) {
        case CpuType::MOS6502:     return CA65_CPU_6502;
        case CpuType::MOS6502X:    return CA65_CPU_6502X;
        case CpuType::MOS6502DTV:  return CA65_CPU_6502DTV;
        case CpuType::WDC65SC02:   return CA65_CPU_65SC02;
        case CpuType::WDC65C02:    return CA65_CPU_65C02;
        case CpuType::WDC65816:    return CA65_CPU_65816;
        case CpuType::HuC6280:     return CA65_CPU_HUC6280;
        case CpuType::CSG4510:     return CA65_CPU_4510;
        case CpuType::CSG45GS02:   return CA65_CPU_45GS02;
        case CpuType::M740:        return CA65_CPU_M740;
        case CpuType::Sweet16:     return CA65_CPU_SWEET16;
    }
    return CA65_CPU_6502; // Default fallback
}

Ca65TargetSystem Assembler::convert_target_system(TargetSystem target) noexcept {
    switch (target) {
        case TargetSystem::None:           return CA65_TARGET_NONE;
        case TargetSystem::Atari2600:      return CA65_TARGET_ATARI2600;
        case TargetSystem::Atari5200:      return CA65_TARGET_ATARI5200;
        case TargetSystem::Atari7800:      return CA65_TARGET_ATARI7800;
        case TargetSystem::Atari:          return CA65_TARGET_ATARI;
        case TargetSystem::AtariXL:        return CA65_TARGET_ATARIXL;
        case TargetSystem::Commodore16:    return CA65_TARGET_C16;
        case TargetSystem::Commodore64:    return CA65_TARGET_C64;
        case TargetSystem::Commodore65:    return CA65_TARGET_C65;
        case TargetSystem::VIC20:          return CA65_TARGET_VIC20;
        case TargetSystem::Commodore128:   return CA65_TARGET_C128;
        case TargetSystem::Plus4:          return CA65_TARGET_PLUS4;
        case TargetSystem::CBM510:         return CA65_TARGET_CBM510;
        case TargetSystem::CBM610:         return CA65_TARGET_CBM610;
        case TargetSystem::PET:            return CA65_TARGET_PET;
        case TargetSystem::BBC:            return CA65_TARGET_BBC;
        case TargetSystem::Apple2:         return CA65_TARGET_APPLE2;
        case TargetSystem::Apple2Enhanced: return CA65_TARGET_APPLE2ENH;
        case TargetSystem::NES:            return CA65_TARGET_NES;
        case TargetSystem::Supervision:    return CA65_TARGET_SUPERVISION;
        case TargetSystem::Lynx:           return CA65_TARGET_LYNX;
        case TargetSystem::Sim6502:        return CA65_TARGET_SIM6502;
        case TargetSystem::Sim65C02:       return CA65_TARGET_SIM65C02;
        case TargetSystem::OSI_C1P:        return CA65_TARGET_OSIC1P;
        case TargetSystem::PCEngine:       return CA65_TARGET_PCENGINE;
        case TargetSystem::CommanderX16:   return CA65_TARGET_CX16;
        case TargetSystem::SYM1:           return CA65_TARGET_SYM1;
        case TargetSystem::MEGA65:         return CA65_TARGET_MEGA65;
        case TargetSystem::KIM1:           return CA65_TARGET_KIM1;
        case TargetSystem::RP6502:         return CA65_TARGET_RP6502;
        case TargetSystem::Agat:           return CA65_TARGET_AGAT;
    }
    return CA65_TARGET_NONE; // Default fallback
}

Ca65MemoryModel Assembler::convert_memory_model(MemoryModel model) noexcept {
    switch (model) {
        case MemoryModel::Near: return CA65_MMODEL_NEAR;
        case MemoryModel::Far:  return CA65_MMODEL_FAR;
        case MemoryModel::Huge: return CA65_MMODEL_HUGE;
    }
    return CA65_MMODEL_NEAR; // Default fallback
}

/*****************************************************************************/
/*                              Assembler Class                            */
/*****************************************************************************/

Assembler::Assembler(AssemblerConfig config)
    : config_(std::move(config))
    , context_(nullptr, ca65_context_free) {
    
    // Initialize ca65 library if not already done
    static bool library_initialized = []() {
        if (!ca65_init()) {
            throw std::runtime_error("Failed to initialize ca65 library");
        }
        return true;
    }();
    
    // Create C configuration
    Ca65Config c_config{};
    c_config.CPU = convert_cpu_type(config_.cpu());
    c_config.Target = convert_target_system(config_.target());
    c_config.MemoryModel = convert_memory_model(config_.memory_model());
    c_config.DebugInfo = config_.debug_info() ? 1 : 0;
    c_config.SmartMode = config_.smart_mode() ? 1 : 0;
    c_config.IgnoreCase = config_.ignore_case() ? 1 : 0;
    c_config.AutoImport = config_.auto_import() ? 1 : 0;
    c_config.WarningsAsErrors = config_.warnings_as_errors() ? 1 : 0;
    
    // Convert include paths to C array
    std::vector<const char*> include_paths_c;
    include_paths_c.reserve(config_.include_paths().size() + 1);
    for (const auto& path : config_.include_paths()) {
        include_paths_c.push_back(path.c_str());
    }
    include_paths_c.push_back(nullptr); // Null terminator
    c_config.IncludePaths = include_paths_c.data();
    
    // Convert defines to C array
    std::vector<const char*> defines_c;
    defines_c.reserve(config_.defines().size() + 1);
    for (const auto& define : config_.defines()) {
        defines_c.push_back(define.c_str());
    }
    defines_c.push_back(nullptr); // Null terminator
    c_config.Defines = defines_c.data();
    
    // Create context
    context_.reset(ca65_context_new_with_config(&c_config));
    if (!context_) {
        throw std::runtime_error("Failed to create ca65 context");
    }
}

Assembler::~Assembler() = default;

Assembler::Assembler(Assembler&&) noexcept = default;
Assembler& Assembler::operator=(Assembler&&) noexcept = default;

AssemblyResult Assembler::assemble_impl(std::string_view source) {
    if (!context_) {
        return std::unexpected(AssemblyError("Assembler not properly initialized"));
    }
    
    // Call ca65 library function
    Ca65Result* result = ca65_assemble_string(context_.get(), source.data());
    if (!result) {
        return std::unexpected(AssemblyError("Assembly failed - no result returned"));
    }
    
    // RAII wrapper for result
    std::unique_ptr<Ca65Result, void(*)(Ca65Result*)> result_guard(
        result, ca65_result_free);
    
    // Check for errors
    if (result->ErrorCount > 0) {
        std::string error_msg = "Assembly failed with errors:\n";
        for (int i = 0; i < result->ErrorCount; ++i) {
            const char* error = ca65_result_get_error(result, i);
            if (error) {
                error_msg += std::format("  {}: {}\n", i + 1, error);
            }
        }
        return std::unexpected(AssemblyError(std::move(error_msg)));
    }
    
    // Convert object data to std::vector<std::byte>
    std::vector<std::byte> object_code;
    if (result->ObjectSize > 0 && result->ObjectData) {
        object_code.reserve(result->ObjectSize);
        auto* data = reinterpret_cast<const std::byte*>(result->ObjectData);
        object_code.assign(data, data + result->ObjectSize);
    }
    
    // Collect warnings
    std::vector<AssemblyWarning> warnings;
    warnings.reserve(result->WarningCount);
    for (int i = 0; i < result->WarningCount; ++i) {
        const char* warning = ca65_result_get_warning(result, i);
        if (warning) {
            warnings.emplace_back(warning);
        }
    }
    
    return AssemblyOutput(std::move(object_code), std::move(warnings));
}

AssemblyResult Assembler::assemble_buffer(const char* data, std::size_t size) {
    if (!context_) {
        return std::unexpected(AssemblyError("Assembler not properly initialized"));
    }
    
    Ca65Result* result = ca65_assemble_buffer(context_.get(), data, size);
    if (!result) {
        return std::unexpected(AssemblyError("Assembly failed - no result returned"));
    }
    
    // Similar processing as assemble_impl...
    // (Implementation would be similar to above)
    
    return std::unexpected(AssemblyError("Not yet implemented"));
}

void Assembler::define_symbol(std::string_view name, std::integral auto value) {
    if (context_) {
        ca65_context_define_symbol(context_.get(), name.data(), 
                                   static_cast<long>(value));
    }
}

void Assembler::add_include_path(std::string_view path) {
    if (context_) {
        ca65_context_add_include_path(context_.get(), path.data());
    }
}

/*****************************************************************************/
/*                              Factory Functions                           */
/*****************************************************************************/

Assembler make_c64_assembler() {
    return Assembler{
        AssemblerConfig{}
            .target(TargetSystem::Commodore64)
            .cpu(CpuType::MOS6502)
            .memory_model(MemoryModel::Near)
    };
}

Assembler make_nes_assembler() {
    return Assembler{
        AssemblerConfig{}
            .target(TargetSystem::NES)
            .cpu(CpuType::MOS6502)
            .memory_model(MemoryModel::Near)
    };
}

Assembler make_apple2_assembler() {
    return Assembler{
        AssemblerConfig{}
            .target(TargetSystem::Apple2)
            .cpu(CpuType::MOS6502)
            .memory_model(MemoryModel::Near)
    };
}

Assembler make_generic_6502_assembler() {
    return Assembler{
        AssemblerConfig{}
            .target(TargetSystem::None)
            .cpu(CpuType::MOS6502)
            .memory_model(MemoryModel::Near)
    };
}

/*****************************************************************************/
/*                              Builder Pattern                             */
/*****************************************************************************/

AssemblerBuilder& AssemblerBuilder::for_target(TargetSystem target) {
    config_.target(target);
    return *this;
}

AssemblerBuilder& AssemblerBuilder::with_cpu(CpuType cpu) {
    config_.cpu(cpu);
    return *this;
}

AssemblerBuilder& AssemblerBuilder::with_debug_info() {
    config_.debug_info(true);
    return *this;
}

AssemblerBuilder& AssemblerBuilder::with_smart_mode() {
    config_.smart_mode(true);
    return *this;
}

AssemblerBuilder& AssemblerBuilder::case_insensitive() {
    config_.ignore_case(true);
    return *this;
}

AssemblerBuilder& AssemblerBuilder::include_path(std::string_view path) {
    config_.include_path(path);
    return *this;
}

Assembler AssemblerBuilder::build() {
    return Assembler{std::move(config_)};
}

} // namespace next::assembler
