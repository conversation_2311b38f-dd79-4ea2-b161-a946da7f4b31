# Makefile for building ca65 as a library
# This builds ca65's core functionality as a static library for use in NExT

# Paths - adjust these to match your cc65 source location
CC65_SRC = ../cc65/src
CA65_SRC = $(CC65_SRC)/ca65
COMMON_SRC = $(CC65_SRC)/common

# Compiler settings
CC = gcc
CFLAGS = -Wall -Wextra -O2 -std=c99 -fPIC
INCLUDES = -I$(CC65_SRC) -I$(CA65_SRC) -I$(COMMON_SRC) -I.

# Library name
LIBNAME = libca65
STATIC_LIB = $(LIBNAME).a
SHARED_LIB = $(LIBNAME).so

# Source files from ca65 (excluding main.c)
CA65_SOURCES = \
	$(CA65_SRC)/anonname.c \
	$(CA65_SRC)/asserts.c \
	$(CA65_SRC)/condasm.c \
	$(CA65_SRC)/dbginfo.c \
	$(CA65_SRC)/ea65.c \
	$(CA65_SRC)/easw16.c \
	$(CA65_SRC)/enum.c \
	$(CA65_SRC)/error.c \
	$(CA65_SRC)/expr.c \
	$(CA65_SRC)/feature.c \
	$(CA65_SRC)/filetab.c \
	$(CA65_SRC)/fragment.c \
	$(CA65_SRC)/global.c \
	$(CA65_SRC)/incpath.c \
	$(CA65_SRC)/instr.c \
	$(CA65_SRC)/istack.c \
	$(CA65_SRC)/lineinfo.c \
	$(CA65_SRC)/listing.c \
	$(CA65_SRC)/macro.c \
	$(CA65_SRC)/nexttok.c \
	$(CA65_SRC)/objcode.c \
	$(CA65_SRC)/objfile.c \
	$(CA65_SRC)/options.c \
	$(CA65_SRC)/pseudo.c \
	$(CA65_SRC)/repeat.c \
	$(CA65_SRC)/scanner.c \
	$(CA65_SRC)/segdef.c \
	$(CA65_SRC)/segment.c \
	$(CA65_SRC)/sizeof.c \
	$(CA65_SRC)/span.c \
	$(CA65_SRC)/spool.c \
	$(CA65_SRC)/struct.c \
	$(CA65_SRC)/studyexpr.c \
	$(CA65_SRC)/symbol.c \
	$(CA65_SRC)/symentry.c \
	$(CA65_SRC)/symtab.c \
	$(CA65_SRC)/token.c \
	$(CA65_SRC)/toklist.c \
	$(CA65_SRC)/ulabel.c

# Source files from common
COMMON_SOURCES = \
	$(COMMON_SRC)/abend.c \
	$(COMMON_SRC)/addrsize.c \
	$(COMMON_SRC)/alignment.c \
	$(COMMON_SRC)/bitops.c \
	$(COMMON_SRC)/chartype.c \
	$(COMMON_SRC)/check.c \
	$(COMMON_SRC)/cmdline.c \
	$(COMMON_SRC)/coll.c \
	$(COMMON_SRC)/cpu.c \
	$(COMMON_SRC)/debugflag.c \
	$(COMMON_SRC)/exprdefs.c \
	$(COMMON_SRC)/filepos.c \
	$(COMMON_SRC)/filetype.c \
	$(COMMON_SRC)/fname.c \
	$(COMMON_SRC)/hashstr.c \
	$(COMMON_SRC)/hashtab.c \
	$(COMMON_SRC)/intstack.c \
	$(COMMON_SRC)/mmodel.c \
	$(COMMON_SRC)/print.c \
	$(COMMON_SRC)/searchpath.c \
	$(COMMON_SRC)/segnames.c \
	$(COMMON_SRC)/strbuf.c \
	$(COMMON_SRC)/strpool.c \
	$(COMMON_SRC)/strstack.c \
	$(COMMON_SRC)/target.c \
	$(COMMON_SRC)/tgttrans.c \
	$(COMMON_SRC)/version.c \
	$(COMMON_SRC)/xmalloc.c \
	$(COMMON_SRC)/xsprintf.c

# Our compatibility layer
COMPAT_SOURCES = ca65_lib.c

# All sources
ALL_SOURCES = $(CA65_SOURCES) $(COMMON_SOURCES) $(COMPAT_SOURCES)

# Object files
CA65_OBJECTS = $(CA65_SOURCES:.c=.o)
COMMON_OBJECTS = $(COMMON_SOURCES:.c=.o)
COMPAT_OBJECTS = $(COMPAT_SOURCES:.c=.o)
ALL_OBJECTS = $(CA65_OBJECTS) $(COMMON_OBJECTS) $(COMPAT_OBJECTS)

# Default target
all: $(STATIC_LIB) $(SHARED_LIB)

# Static library
$(STATIC_LIB): $(ALL_OBJECTS)
	ar rcs $@ $^

# Shared library
$(SHARED_LIB): $(ALL_OBJECTS)
	$(CC) -shared -o $@ $^

# Compile rules
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Special handling for our compatibility layer
ca65_lib.o: ca65_lib.c ca65_lib.h
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Test program
test: test_ca65lib.c $(STATIC_LIB)
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $< -L. -lca65

# Clean
clean:
	rm -f $(ALL_OBJECTS) $(STATIC_LIB) $(SHARED_LIB) test

# Install (adjust paths as needed)
install: $(STATIC_LIB) $(SHARED_LIB)
	install -d $(DESTDIR)/usr/local/lib
	install -d $(DESTDIR)/usr/local/include
	install -m 644 $(STATIC_LIB) $(DESTDIR)/usr/local/lib/
	install -m 755 $(SHARED_LIB) $(DESTDIR)/usr/local/lib/
	install -m 644 ca65_lib.h $(DESTDIR)/usr/local/include/

# Dependencies
depend:
	$(CC) $(INCLUDES) -MM $(ALL_SOURCES) > .depend

# Include dependencies if they exist
-include .depend

.PHONY: all clean install depend test
