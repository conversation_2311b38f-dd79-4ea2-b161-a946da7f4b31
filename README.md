# NExT Language Project

## Overview

NExT (Nostalgic Extensible Templated) is a modern programming language for retro gaming and retrocomputing that replaces traditional macro assembler macros with modern templates, which are particularized at compile time.

## Project Structure

```
NExT/
├── README.md
├── CMakeLists.txt                 # Root CMake configuration
├── 3rdparty/                      # Third-party dependencies
│   └── cc65/                      # Original cc65 source code
├── src/
│   └── Assembler/
│       └── 6502/                  # 6502 architecture backend
│           ├── CMakeLists.txt     # 6502 backend build configuration
│           ├── Include/           # Public headers
│           │   └── NExT/
│           │       └── Assembler/
│           │           └── 6502/
│           │               ├── Assembler.hpp
│           │               ├── Config.hpp
│           │               └── Types.hpp
│           ├── Source/            # Implementation files
│           │   ├── Assembler.cpp
│           │   ├── Config.cpp
│           │   ├── Ca65Wrapper.cpp
│           │   └── Ca65Wrapper.h  # C compatibility layer
│           └── Tests/             # Unit tests
│               ├── CMakeLists.txt
│               ├── TestAssembler.cpp
│               └── TestConfig.cpp
├── examples/                      # Example assembly files
│   ├── c64/
│   ├── nes/
│   └── apple2/
└── docs/                          # Documentation
    ├── architecture.md
    └── api-reference.md
```

## Current Phase: 6502 Backend

This phase focuses on creating a robust 6502 assembler backend using ca65 as the foundation, wrapped in a modern C++23 API.

### Goals

1. **Library Compilation**: Compile ca65 as a static/shared library
2. **C++ Wrapper**: Modern C++23 interface with RAII, concepts, and std::expected
3. **Architecture Separation**: Clean separation allowing future architectures (Z80, 68000, etc.)
4. **Versatile API**: Flexible configuration and usage patterns

### Architecture

```
NExT::Assembler::6502::Assembler
           ↓
    C++ Wrapper Layer (C++23)
           ↓
    C Compatibility Layer
           ↓
    ca65 Core Library (Unmodified)
```

## Building

### Prerequisites

- C++23 compatible compiler (GCC 13+, Clang 16+, MSVC 2022)
- CMake 3.25+
- Git

### Build Steps

```bash
# Clone the repository
git clone <repository-url> NExT
cd NExT

# Initialize and update submodules (for cc65)
git submodule update --init --recursive

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_CXX_STANDARD=23

# Build
cmake --build .

# Run tests
ctest
```

## Usage Example

```cpp
import NExT.Assembler.6502;

using namespace NExT::Assembler::M6502;

auto assembler = AssemblerBuilder{}
    .ForTarget(TargetSystem::Commodore64)
    .WithCpu(CpuType::MOS6502)
    .WithDebugInfo()
    .IncludePath("./include")
    .Define("SCREEN_RAM", 0x0400)
    .Build();

std::string_view source = R"(
    .segment "CODE"
    start:
        lda #$00
        sta SCREEN_RAM
        rts
)";

auto result = assembler.Assemble(source);
if (result) {
    auto objectCode = result->ObjectCode();
    // Use object code...
} else {
    std::println("Error: {}", result.error().Message);
}
```

## License

This project uses ca65 from the cc65 suite, which is licensed under the zlib license. See the cc65 license for details.

## Contributing

Please read the architecture documentation before contributing to understand the design principles and separation of concerns.
