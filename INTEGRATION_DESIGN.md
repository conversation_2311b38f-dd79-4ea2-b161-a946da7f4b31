# NExT Language ca65 Integration Design

## Overview

This document describes the design for integrating ca65 as a library backend for the NExT language assembler. The approach uses a compatibility layer that wraps ca65's core functionality without modifying the original ca65 source code.

## Architecture

```
NExT Source Code
       ↓
NExT Template Engine (Preprocessor)
       ↓
Generated Assembly Code
       ↓
ca65 Library Interface (Compatibility Layer)
       ↓
ca65 Core Engine (Unmodified)
       ↓
Object Code Output
```

## Key Components

### 1. ca65 Library Interface (`ca65_lib.h/c`)

**Purpose**: Provides a clean C API that wraps ca65's internal functionality.

**Key Features**:
- Context-based API (multiple assembler instances)
- Memory-based input/output (no file I/O required)
- Comprehensive error/warning collection
- Configuration management
- Symbol definition support

**API Functions**:
- `ca65_init()` / `ca65_cleanup()` - Library lifecycle
- `ca65_context_new()` / `ca65_context_free()` - Context management
- `ca65_assemble_string()` / `ca65_assemble_buffer()` - Assembly functions
- Configuration setters for CPU, target, memory model, etc.

### 2. Build System (`Makefile.ca65lib`)

**Purpose**: Compiles ca65 as a static/shared library instead of executable.

**Approach**:
- Excludes `main.c` from ca65 sources
- Includes all core ca65 modules and common utilities
- Builds both static (`.a`) and shared (`.so`) libraries
- Provides installation targets

### 3. NExT Preprocessor (To be implemented)

**Purpose**: Transforms NExT template syntax into standard assembly code.

**Responsibilities**:
- Parse NExT template definitions
- Instantiate templates with parameters
- Generate clean assembly code compatible with ca65
- Handle template-specific error reporting

## Integration Strategy

### Phase 1: Library Compilation
1. Extract ca65 core functionality into library
2. Create compatibility layer API
3. Implement basic assembly functions
4. Test with simple assembly code

### Phase 2: Memory I/O Implementation
1. Modify ca65's scanner to accept memory input
2. Redirect object file output to memory buffers
3. Implement error/warning collection
4. Test with complex assembly programs

### Phase 3: NExT Preprocessor
1. Design NExT template syntax
2. Implement template parser
3. Create template instantiation engine
4. Integrate with ca65 library

### Phase 4: Full Integration
1. Complete error handling and reporting
2. Add debugging support
3. Performance optimization
4. Documentation and examples

## Key Modifications Required

### Input Handling
- Modify `scanner.c` to accept string input instead of file input
- Implement `NewInputData()` function for memory-based input
- Ensure proper cleanup of input resources

### Output Handling
- Modify `objfile.c` to write to memory buffer instead of file
- Implement buffer management for object data
- Provide access to generated object code

### Error Collection
- Capture error and warning messages instead of printing to stderr
- Store messages in context for later retrieval
- Provide API functions to access error information

### Global State Management
- Ensure ca65's global variables are properly initialized/reset
- Handle multiple concurrent assembler contexts
- Prevent state leakage between assembly sessions

## Benefits of This Approach

### 1. **No ca65 Modifications**
- Preserves ability to update ca65 from upstream
- Maintains compatibility with existing ca65 ecosystem
- Reduces maintenance burden

### 2. **Clean Separation**
- Template processing separate from assembly
- Clear API boundaries
- Easier testing and debugging

### 3. **Flexibility**
- Can swap out ca65 for other assemblers if needed
- Template engine can target multiple backends
- Modular architecture supports incremental development

### 4. **Proven Foundation**
- Leverages ca65's mature 6502/65816 support
- Benefits from ca65's extensive testing
- Inherits ca65's CPU variant support

## Implementation Notes

### Memory Management
- All dynamically allocated memory must be properly tracked
- Context objects own their resources
- Result objects must be explicitly freed

### Thread Safety
- Current design assumes single-threaded usage
- Global state in ca65 may require synchronization for multi-threading
- Consider thread-local storage for global variables if needed

### Error Handling
- Library functions return NULL/0 on failure
- Detailed error information available through result objects
- No calls to `exit()` or `abort()` from library code

### Performance Considerations
- Minimize memory allocations during assembly
- Reuse contexts when possible
- Consider object code caching for repeated assemblies

## Testing Strategy

### Unit Tests
- Test each API function individually
- Verify error handling and edge cases
- Test memory management (no leaks)

### Integration Tests
- Assemble real-world 6502 programs
- Compare output with standalone ca65
- Test various CPU targets and configurations

### Performance Tests
- Measure assembly speed vs. standalone ca65
- Test memory usage patterns
- Benchmark template instantiation overhead

## Future Enhancements

### Advanced Features
- Incremental assembly support
- Symbol table introspection
- Custom segment handling
- Macro expansion hooks

### Optimization Opportunities
- Template caching
- Parallel template instantiation
- Optimized object code generation
- Memory pool allocation

This design provides a solid foundation for integrating ca65 into the NExT language while maintaining flexibility and avoiding modifications to the upstream ca65 codebase.
