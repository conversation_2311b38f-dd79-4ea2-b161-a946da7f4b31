/*****************************************************************************/
/*                                                                           */
/*                               Assembler.hpp                              */
/*                                                                           */
/*                      Main interface for NExT 6502 Assembler              */
/*                                                                           */
/*****************************************************************************/

#pragma once

#include "Types.hpp"
#include "Config.hpp"
#include <memory>
#include <string_view>
#include <filesystem>

namespace NExT::Assembler::M6502 {

/*****************************************************************************/
/*                              Forward Declarations                        */
/*****************************************************************************/

struct Ca65Context; // Opaque handle to ca65 context

/*****************************************************************************/
/*                              Main Assembler Class                        */
/*****************************************************************************/

class Assembler {
public:
    // Construction and destruction
    explicit Assembler(AssemblerConfig config = {});
    ~Assembler();
    
    // Non-copyable, movable
    Assembler(const Assembler&) = delete;
    Assembler& operator=(const Assembler&) = delete;
    Assembler(Assembler&&) noexcept;
    Assembler& operator=(Assembler&&) noexcept;
    
    // Assembly functions
    template<StringLike T>
    AssemblyResult Assemble(T&& source) {
        return AssembleImpl(std::string_view{source});
    }
    
    template<AssemblySource T>
    AssemblyResult Assemble(const T& source) {
        return AssembleBuffer(source.data(), source.size());
    }
    
    AssemblyResult AssembleFile(const std::filesystem::path& filename);
    
    // Configuration management
    void Reconfigure(AssemblerConfig newConfig);
    const AssemblerConfig& Config() const noexcept { return config_; }
    
    // Runtime symbol management
    template<IntegralValue T>
    void DefineSymbol(std::string_view name, T value) {
        DefineSymbolImpl(name, static_cast<long>(value));
    }
    
    void AddIncludePath(std::string_view path);
    
    // State management
    void Reset(); // Reset assembler state for new assembly
    bool IsInitialized() const noexcept;
    
    // Information queries
    std::vector<std::string> GetDefinedSymbols() const;
    std::vector<std::string> GetIncludePaths() const;

private:
    // Implementation details
    AssemblyResult AssembleImpl(std::string_view source);
    AssemblyResult AssembleBuffer(const char* data, std::size_t size);
    void DefineSymbolImpl(std::string_view name, long value);
    
    // Initialization and cleanup
    void InitializeContext();
    void CleanupContext();
    
    // Configuration
    AssemblerConfig config_;
    
    // ca65 context (PIMPL idiom)
    std::unique_ptr<Ca65Context, void(*)(Ca65Context*)> context_;
    
    // State
    bool initialized_{false};
};

/*****************************************************************************/
/*                              Factory Functions                           */
/*****************************************************************************/

// Factory functions for common configurations
Assembler MakeC64Assembler();
Assembler MakeNESAssembler();
Assembler MakeApple2Assembler();
Assembler MakeAtariAssembler();
Assembler MakeGeneric6502Assembler();

// Advanced factory with custom configuration
Assembler MakeAssembler(const AssemblerConfig& config);

// Builder-based factory
Assembler MakeAssembler(const AssemblerBuilder& builder);

/*****************************************************************************/
/*                              Utility Functions                           */
/*****************************************************************************/

// Get information about supported targets and CPUs
std::vector<TargetSystem> GetSupportedTargets();
std::vector<CpuType> GetSupportedCpus();
std::string GetTargetName(TargetSystem target);
std::string GetCpuName(CpuType cpu);

// Validation utilities
bool IsValidCpuForTarget(CpuType cpu, TargetSystem target);
std::vector<CpuType> GetCompatibleCpus(TargetSystem target);

// Version information
std::string GetVersion();
std::string GetCa65Version();

} // namespace NExT::Assembler::M6502
