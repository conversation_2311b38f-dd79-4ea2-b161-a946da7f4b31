/*****************************************************************************/
/*                                                                           */
/*                            next_assembler.cppm                           */
/*                                                                           */
/*                    Modern C++23 Assembler Interface Module               */
/*                                                                           */
/*                                                                           */
/*                                                                           */
/* This module provides a modern C++23 interface for the NExT language      */
/* assembler, wrapping the ca65 compatibility layer with RAII, concepts,    */
/* ranges, and other modern C++ features.                                   */
/*                                                                           */
/*****************************************************************************/

module;

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <span>
#include <expected>
#include <ranges>
#include <concepts>
#include <format>
#include <source_location>

// C compatibility layer
extern "C" {
#include "ca65_lib.h"
}

export module next.assembler;

export namespace next::assembler {

/*****************************************************************************/
/*                                 Concepts                                  */
/*****************************************************************************/

template<typename T>
concept StringLike = std::convertible_to<T, std::string_view>;

template<typename T>
concept AssemblySource = requires(T t) {
    { t.data() } -> std::convertible_to<const char*>;
    { t.size() } -> std::convertible_to<std::size_t>;
};

template<typename T>
concept ConfigurableTarget = requires(T t) {
    { t.cpu() } -> std::same_as<CpuType>;
    { t.target() } -> std::same_as<TargetSystem>;
    { t.memory_model() } -> std::same_as<MemoryModel>;
};

/*****************************************************************************/
/*                                   Enums                                  */
/*****************************************************************************/

enum class CpuType {
    MOS6502,
    MOS6502X,
    MOS6502DTV,
    WDC65SC02,
    WDC65C02,
    WDC65816,
    HuC6280,
    CSG4510,
    CSG45GS02,
    M740,
    Sweet16
};

enum class TargetSystem {
    None,
    Atari2600, Atari5200, Atari7800, Atari, AtariXL,
    Commodore16, Commodore64, Commodore65, VIC20, Commodore128,
    Plus4, CBM510, CBM610, PET, BBC,
    Apple2, Apple2Enhanced, NES, Supervision, Lynx,
    Sim6502, Sim65C02, OSI_C1P, PCEngine, CommanderX16,
    SYM1, MEGA65, KIM1, RP6502, Agat
};

enum class MemoryModel {
    Near, Far, Huge
};

/*****************************************************************************/
/*                              Error Handling                              */
/*****************************************************************************/

struct AssemblyError {
    std::string message;
    std::source_location location;
    int line_number{0};
    int column{0};
    
    AssemblyError(std::string msg, 
                  std::source_location loc = std::source_location::current())
        : message(std::move(msg)), location(loc) {}
};

struct AssemblyWarning {
    std::string message;
    int line_number{0};
    int column{0};
    
    AssemblyWarning(std::string msg) : message(std::move(msg)) {}
};

using AssemblyResult = std::expected<std::vector<std::byte>, AssemblyError>;

/*****************************************************************************/
/*                              Configuration                               */
/*****************************************************************************/

class AssemblerConfig {
public:
    AssemblerConfig() = default;
    
    constexpr AssemblerConfig& cpu(CpuType type) noexcept {
        cpu_ = type;
        return *this;
    }
    
    constexpr AssemblerConfig& target(TargetSystem sys) noexcept {
        target_ = sys;
        return *this;
    }
    
    constexpr AssemblerConfig& memory_model(MemoryModel model) noexcept {
        memory_model_ = model;
        return *this;
    }
    
    constexpr AssemblerConfig& debug_info(bool enable = true) noexcept {
        debug_info_ = enable;
        return *this;
    }
    
    constexpr AssemblerConfig& smart_mode(bool enable = true) noexcept {
        smart_mode_ = enable;
        return *this;
    }
    
    constexpr AssemblerConfig& ignore_case(bool enable = true) noexcept {
        ignore_case_ = enable;
        return *this;
    }
    
    constexpr AssemblerConfig& auto_import(bool enable = true) noexcept {
        auto_import_ = enable;
        return *this;
    }
    
    constexpr AssemblerConfig& warnings_as_errors(bool enable = true) noexcept {
        warnings_as_errors_ = enable;
        return *this;
    }
    
    AssemblerConfig& include_path(std::string_view path) {
        include_paths_.emplace_back(path);
        return *this;
    }
    
    template<std::ranges::range R>
    AssemblerConfig& include_paths(R&& paths) {
        for (auto&& path : paths) {
            include_paths_.emplace_back(path);
        }
        return *this;
    }
    
    AssemblerConfig& define(std::string_view name, std::integral auto value) {
        defines_.emplace_back(std::format("{}={}", name, value));
        return *this;
    }
    
    AssemblerConfig& define(std::string_view name, std::string_view value) {
        defines_.emplace_back(std::format("{}={}", name, value));
        return *this;
    }
    
    // Getters
    constexpr CpuType cpu() const noexcept { return cpu_; }
    constexpr TargetSystem target() const noexcept { return target_; }
    constexpr MemoryModel memory_model() const noexcept { return memory_model_; }
    constexpr bool debug_info() const noexcept { return debug_info_; }
    constexpr bool smart_mode() const noexcept { return smart_mode_; }
    constexpr bool ignore_case() const noexcept { return ignore_case_; }
    constexpr bool auto_import() const noexcept { return auto_import_; }
    constexpr bool warnings_as_errors() const noexcept { return warnings_as_errors_; }
    
    const std::vector<std::string>& include_paths() const noexcept { 
        return include_paths_; 
    }
    
    const std::vector<std::string>& defines() const noexcept { 
        return defines_; 
    }

private:
    CpuType cpu_{CpuType::MOS6502};
    TargetSystem target_{TargetSystem::None};
    MemoryModel memory_model_{MemoryModel::Near};
    bool debug_info_{false};
    bool smart_mode_{false};
    bool ignore_case_{false};
    bool auto_import_{false};
    bool warnings_as_errors_{false};
    std::vector<std::string> include_paths_;
    std::vector<std::string> defines_;
};

/*****************************************************************************/
/*                              Assembly Result                             */
/*****************************************************************************/

class AssemblyOutput {
public:
    AssemblyOutput(std::vector<std::byte> object_code,
                   std::vector<AssemblyWarning> warnings = {})
        : object_code_(std::move(object_code))
        , warnings_(std::move(warnings)) {}
    
    const std::vector<std::byte>& object_code() const noexcept {
        return object_code_;
    }
    
    std::span<const std::byte> object_span() const noexcept {
        return object_code_;
    }
    
    const std::vector<AssemblyWarning>& warnings() const noexcept {
        return warnings_;
    }
    
    bool has_warnings() const noexcept {
        return !warnings_.empty();
    }
    
    std::size_t size() const noexcept {
        return object_code_.size();
    }
    
    bool empty() const noexcept {
        return object_code_.empty();
    }

private:
    std::vector<std::byte> object_code_;
    std::vector<AssemblyWarning> warnings_;
};

/*****************************************************************************/
/*                              Main Interface                              */
/*****************************************************************************/

class Assembler {
public:
    explicit Assembler(AssemblerConfig config = {});
    ~Assembler();
    
    // Non-copyable, movable
    Assembler(const Assembler&) = delete;
    Assembler& operator=(const Assembler&) = delete;
    Assembler(Assembler&&) noexcept;
    Assembler& operator=(Assembler&&) noexcept;
    
    // Assembly functions
    AssemblyResult assemble(StringLike auto&& source) {
        return assemble_impl(std::string_view{source});
    }
    
    template<AssemblySource T>
    AssemblyResult assemble(const T& source) {
        return assemble_buffer(source.data(), source.size());
    }
    
    AssemblyResult assemble_file(std::string_view filename);
    
    // Configuration
    void reconfigure(AssemblerConfig new_config);
    const AssemblerConfig& config() const noexcept { return config_; }
    
    // Symbol management
    void define_symbol(std::string_view name, std::integral auto value);
    void add_include_path(std::string_view path);

private:
    AssemblyResult assemble_impl(std::string_view source);
    AssemblyResult assemble_buffer(const char* data, std::size_t size);
    
    AssemblerConfig config_;
    std::unique_ptr<Ca65Context, void(*)(Ca65Context*)> context_;
    
    static Ca65CpuType convert_cpu_type(CpuType cpu) noexcept;
    static Ca65TargetSystem convert_target_system(TargetSystem target) noexcept;
    static Ca65MemoryModel convert_memory_model(MemoryModel model) noexcept;
};

/*****************************************************************************/
/*                              Factory Functions                           */
/*****************************************************************************/

// Factory functions for common configurations
Assembler make_c64_assembler();
Assembler make_nes_assembler();
Assembler make_apple2_assembler();
Assembler make_generic_6502_assembler();

// Builder pattern for complex configurations
class AssemblerBuilder {
public:
    AssemblerBuilder& for_target(TargetSystem target);
    AssemblerBuilder& with_cpu(CpuType cpu);
    AssemblerBuilder& with_debug_info();
    AssemblerBuilder& with_smart_mode();
    AssemblerBuilder& case_insensitive();
    AssemblerBuilder& include_path(std::string_view path);
    AssemblerBuilder& define(std::string_view name, auto value);
    
    Assembler build();

private:
    AssemblerConfig config_;
};

} // namespace next::assembler
