# 6502 Examples for NExT

cmake_minimum_required(VERSION 3.25)

# Example programs
set(EXAMPLES
    basic_assembly
    c64_demo
    nes_demo
)

# Create example executables
foreach(example ${EXAMPLES})
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/${example}.cpp")
        add_executable(${example} ${example}.cpp)
        
        set_target_properties(${example} PROPERTIES
            CXX_STANDARD 23
            CXX_STANDARD_REQUIRED ON
        )
        
        target_link_libraries(${example}
            PRIVATE
                NExT::Assembler::6502
        )
        
        # Install examples
        install(TARGETS ${example}
            RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}/examples
        )
    endif()
endforeach()

# Install assembly source files
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION ${CMAKE_INSTALL_DATADIR}/NExT/examples/6502
    FILES_MATCHING PATTERN "*.asm" PATTERN "*.s"
)
