/*****************************************************************************/
/*                                                                           */
/*                                ca65_lib.c                                */
/*                                                                           */
/*                    Library interface for ca65 assembler                  */
/*                                                                           */
/*                                                                           */
/*                                                                           */
/* This is a compatibility layer that wraps ca65's core functionality       */
/* to provide a clean library interface for the NExT language project.     */
/*                                                                           */
/*****************************************************************************/

#include "ca65_lib.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

/* Include ca65 headers - these would need to be adjusted based on actual paths */
#include "common/target.h"
#include "common/mmodel.h"
#include "ca65/global.h"
#include "ca65/scanner.h"
#include "ca65/symbol.h"
#include "ca65/symtab.h"
#include "ca65/segment.h"
#include "ca65/spool.h"
#include "ca65/lineinfo.h"
#include "ca65/incpath.h"
#include "ca65/objfile.h"
#include "ca65/error.h"
#include "ca65/expr.h"
#include "ca65/instr.h"
#include "ca65/pseudo.h"
#include "ca65/nexttok.h"

/*****************************************************************************/
/*                                   Data                                   */
/*****************************************************************************/

/* Internal context structure */
struct Ca65Context {
    Ca65Config      config;
    int             initialized;
    char**          include_paths;
    size_t          include_path_count;
    char**          defines;
    size_t          define_count;
    /* Error collection */
    char**          errors;
    size_t          error_count;
    char**          warnings;
    size_t          warning_count;
};

/* Global library state */
static int g_library_initialized = 0;

/*****************************************************************************/
/*                              Helper Functions                            */
/*****************************************************************************/

/* Convert our CPU enum to ca65's internal CPU type */
static cpu_t convert_cpu_type(Ca65CpuType cpu) {
    switch (cpu) {
        case CA65_CPU_6502:     return CPU_6502;
        case CA65_CPU_6502X:    return CPU_6502X;
        case CA65_CPU_6502DTV:  return CPU_6502DTV;
        case CA65_CPU_65SC02:   return CPU_65SC02;
        case CA65_CPU_65C02:    return CPU_65C02;
        case CA65_CPU_65816:    return CPU_65816;
        case CA65_CPU_HUC6280:  return CPU_HUC6280;
        case CA65_CPU_4510:     return CPU_4510;
        case CA65_CPU_45GS02:   return CPU_45GS02;
        case CA65_CPU_M740:     return CPU_M740;
        case CA65_CPU_SWEET16:  return CPU_SWEET16;
        default:                return CPU_6502;
    }
}

/* Convert our target enum to ca65's internal target type */
static target_t convert_target_type(Ca65TargetSystem target) {
    switch (target) {
        case CA65_TARGET_NONE:      return TGT_NONE;
        case CA65_TARGET_ATARI2600: return TGT_ATARI2600;
        case CA65_TARGET_ATARI5200: return TGT_ATARI5200;
        case CA65_TARGET_ATARI7800: return TGT_ATARI7800;
        case CA65_TARGET_ATARI:     return TGT_ATARI;
        case CA65_TARGET_ATARIXL:   return TGT_ATARIXL;
        case CA65_TARGET_C16:       return TGT_C16;
        case CA65_TARGET_C64:       return TGT_C64;
        case CA65_TARGET_C65:       return TGT_C65;
        case CA65_TARGET_VIC20:     return TGT_VIC20;
        case CA65_TARGET_C128:      return TGT_C128;
        case CA65_TARGET_PLUS4:     return TGT_PLUS4;
        case CA65_TARGET_CBM510:    return TGT_CBM510;
        case CA65_TARGET_CBM610:    return TGT_CBM610;
        case CA65_TARGET_PET:       return TGT_PET;
        case CA65_TARGET_BBC:       return TGT_BBC;
        case CA65_TARGET_APPLE2:    return TGT_APPLE2;
        case CA65_TARGET_APPLE2ENH: return TGT_APPLE2ENH;
        case CA65_TARGET_NES:       return TGT_NES;
        case CA65_TARGET_SUPERVISION: return TGT_SUPERVISION;
        case CA65_TARGET_LYNX:      return TGT_LYNX;
        case CA65_TARGET_SIM6502:   return TGT_SIM6502;
        case CA65_TARGET_SIM65C02:  return TGT_SIM65C02;
        case CA65_TARGET_OSIC1P:    return TGT_OSIC1P;
        case CA65_TARGET_PCENGINE:  return TGT_PCENGINE;
        case CA65_TARGET_CX16:      return TGT_CX16;
        case CA65_TARGET_SYM1:      return TGT_SYM1;
        case CA65_TARGET_MEGA65:    return TGT_MEGA65;
        case CA65_TARGET_KIM1:      return TGT_KIM1;
        case CA65_TARGET_RP6502:    return TGT_RP6502;
        case CA65_TARGET_AGAT:      return TGT_AGAT;
        default:                    return TGT_NONE;
    }
}

/* Convert our memory model enum to ca65's internal type */
static mmodel_t convert_memory_model(Ca65MemoryModel model) {
    switch (model) {
        case CA65_MMODEL_NEAR:  return MMODEL_NEAR;
        case CA65_MMODEL_FAR:   return MMODEL_FAR;
        case CA65_MMODEL_HUGE:  return MMODEL_HUGE;
        default:                return MMODEL_NEAR;
    }
}

/* Initialize ca65's internal state for library use */
static int init_ca65_internals(Ca65Context* ctx) {
    /* Initialize string pool */
    InitStrPool();
    
    /* Initialize include paths */
    InitIncludePaths();
    
    /* Create predefined segments */
    SegInit();
    
    /* Enter the base lexical level */
    static const StrBuf GlobalNameSpace = STATIC_STRBUF_INITIALIZER;
    SymEnterLevel(&GlobalNameSpace, SCOPE_FILE, ADDR_SIZE_DEFAULT, 0);
    
    /* Initialize line info tracking */
    InitLineInfo();
    
    /* Set CPU type */
    SetCPU(convert_cpu_type(ctx->config.CPU));
    
    /* Set target system */
    Target = convert_target_type(ctx->config.Target);
    
    /* Set memory model */
    SetMemoryModel(convert_memory_model(ctx->config.MemoryModel));
    
    /* Set other options */
    DbgSyms = ctx->config.DebugInfo;
    SmartMode = ctx->config.SmartMode;
    IgnoreCase = ctx->config.IgnoreCase;
    AutoImport = ctx->config.AutoImport;
    RelaxChecks = ctx->config.RelaxChecks;
    WarningsAsErrors = ctx->config.WarningsAsErrors;
    
    /* Add include paths */
    for (size_t i = 0; i < ctx->include_path_count; i++) {
        AddSearchPath(IncSearchPath, ctx->include_paths[i]);
    }
    
    /* Define symbols */
    for (size_t i = 0; i < ctx->define_count; i++) {
        /* Parse "NAME=VALUE" format */
        char* define_copy = strdup(ctx->defines[i]);
        char* equals = strchr(define_copy, '=');
        if (equals) {
            *equals = '\0';
            long value = strtol(equals + 1, NULL, 0);
            /* Call ca65's symbol definition function */
            /* This would need to be implemented based on ca65's internal API */
        }
        free(define_copy);
    }
    
    return 1; /* Success */
}

/*****************************************************************************/
/*                              Public Functions                            */
/*****************************************************************************/

int ca65_init(void) {
    if (g_library_initialized) {
        return 1; /* Already initialized */
    }
    
    /* Initialize any global ca65 state here */
    g_library_initialized = 1;
    return 1;
}

void ca65_cleanup(void) {
    if (!g_library_initialized) {
        return;
    }
    
    /* Cleanup global ca65 state */
    g_library_initialized = 0;
}

Ca65Context* ca65_context_new(void) {
    Ca65Config default_config = ca65_get_default_config(CA65_TARGET_NONE);
    return ca65_context_new_with_config(&default_config);
}

Ca65Context* ca65_context_new_with_config(const Ca65Config* config) {
    if (!g_library_initialized) {
        return NULL;
    }
    
    Ca65Context* ctx = calloc(1, sizeof(Ca65Context));
    if (!ctx) {
        return NULL;
    }
    
    /* Copy configuration */
    ctx->config = *config;
    
    /* Initialize arrays */
    ctx->include_paths = NULL;
    ctx->include_path_count = 0;
    ctx->defines = NULL;
    ctx->define_count = 0;
    ctx->errors = NULL;
    ctx->error_count = 0;
    ctx->warnings = NULL;
    ctx->warning_count = 0;
    
    return ctx;
}

void ca65_context_free(Ca65Context* ctx) {
    if (!ctx) return;
    
    /* Free include paths */
    for (size_t i = 0; i < ctx->include_path_count; i++) {
        free(ctx->include_paths[i]);
    }
    free(ctx->include_paths);
    
    /* Free defines */
    for (size_t i = 0; i < ctx->define_count; i++) {
        free(ctx->defines[i]);
    }
    free(ctx->defines);
    
    /* Free error messages */
    for (size_t i = 0; i < ctx->error_count; i++) {
        free(ctx->errors[i]);
    }
    free(ctx->errors);
    
    /* Free warning messages */
    for (size_t i = 0; i < ctx->warning_count; i++) {
        free(ctx->warnings[i]);
    }
    free(ctx->warnings);
    
    free(ctx);
}

Ca65Config ca65_get_default_config(Ca65TargetSystem target) {
    Ca65Config config = {0};
    
    config.CPU = CA65_CPU_6502;
    config.Target = target;
    config.MemoryModel = CA65_MMODEL_NEAR;
    config.DebugInfo = 0;
    config.SmartMode = 0;
    config.IgnoreCase = 0;
    config.AutoImport = 0;
    config.RelaxChecks = 0;
    config.WarningsAsErrors = 0;
    config.IncludePaths = NULL;
    config.Defines = NULL;
    
    return config;
}

int ca65_validate_config(const Ca65Config* config) {
    if (!config) return 0;
    
    /* Add validation logic here */
    return 1;
}

/* This function would need to be completed with the actual assembly logic */
Ca65Result* ca65_assemble_string(Ca65Context* ctx, const char* source) {
    if (!ctx || !source) {
        return NULL;
    }
    
    /* Initialize ca65 internals for this assembly session */
    if (!init_ca65_internals(ctx)) {
        return NULL;
    }
    
    /* Create result structure */
    Ca65Result* result = calloc(1, sizeof(Ca65Result));
    if (!result) {
        return NULL;
    }
    
    /* TODO: Implement the actual assembly process */
    /* This would involve:
     * 1. Setting up input from string instead of file
     * 2. Running the assembly process
     * 3. Capturing object data to memory instead of file
     * 4. Collecting error/warning messages
     */
    
    return result;
}

Ca65Result* ca65_assemble_buffer(Ca65Context* ctx, const char* buffer, size_t length) {
    /* For now, create a null-terminated string and use assemble_string */
    char* source = malloc(length + 1);
    if (!source) return NULL;
    
    memcpy(source, buffer, length);
    source[length] = '\0';
    
    Ca65Result* result = ca65_assemble_string(ctx, source);
    free(source);
    
    return result;
}

void ca65_result_free(Ca65Result* result) {
    if (!result) return;
    
    free(result->ObjectData);
    free(result->ErrorMessages);
    free(result);
}
