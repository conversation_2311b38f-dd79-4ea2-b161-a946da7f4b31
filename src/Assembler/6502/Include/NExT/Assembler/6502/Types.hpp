/*****************************************************************************/
/*                                                                           */
/*                                  Types.hpp                               */
/*                                                                           */
/*                    Type definitions for NExT 6502 Assembler              */
/*                                                                           */
/*****************************************************************************/

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <span>
#include <expected>
#include <source_location>

namespace NExT::Assembler::M6502 {

/*****************************************************************************/
/*                                CPU Types                                  */
/*****************************************************************************/

enum class CpuType {
    MOS6502,        // Original NMOS 6502
    MOS6502X,       // 6502 with undocumented instructions
    MOS6502DTV,     // C64DTV variant
    WDC65SC02,      // 65SC02 (no bit manipulation, no wai/stp)
    WDC65C02,       // Full 65C02 with bit manipulation
    WDC65816,       // 16-bit 65816 (SNES, Apple IIgs)
    HuC6280,        // PC Engine/TurboGrafx-16 CPU
    CSG4510,        // Commodore 65 CPU
    CSG45GS02,      // MEGA65 CPU
    M740,           // Mitsubishi M740 microcontroller
    Sweet16         // Apple II Sweet16 virtual CPU
};

/*****************************************************************************/
/*                              Target Systems                              */
/*****************************************************************************/

enum class TargetSystem {
    None,
    
    // Atari systems
    Atari2600, Atari5200, Atari7800, Atari, AtariXL,
    
    // Commodore systems
    Commodore16, Commodore64, Commodore65, VIC20, Commodore128,
    Plus4, CBM510, CBM610, PET,
    
    // Apple systems
    Apple2, Apple2Enhanced,
    
    // Other systems
    BBC, NES, Supervision, Lynx, PCEngine, CommanderX16,
    SYM1, MEGA65, KIM1, RP6502, Agat,
    
    // Simulators
    Sim6502, Sim65C02, OSI_C1P
};

/*****************************************************************************/
/*                              Memory Models                               */
/*****************************************************************************/

enum class MemoryModel {
    Near,   // 16-bit addresses
    Far,    // 24-bit addresses  
    Huge    // 32-bit addresses (not supported by ca65)
};

/*****************************************************************************/
/*                              Error Handling                              */
/*****************************************************************************/

struct AssemblyError {
    std::string Message;
    std::source_location Location;
    int LineNumber{0};
    int Column{0};
    
    explicit AssemblyError(std::string message, 
                          std::source_location location = std::source_location::current())
        : Message(std::move(message)), Location(location) {}
};

struct AssemblyWarning {
    std::string Message;
    int LineNumber{0};
    int Column{0};
    
    explicit AssemblyWarning(std::string message) 
        : Message(std::move(message)) {}
};

/*****************************************************************************/
/*                              Assembly Output                             */
/*****************************************************************************/

class AssemblyOutput {
public:
    AssemblyOutput(std::vector<std::byte> objectCode,
                   std::vector<AssemblyWarning> warnings = {})
        : objectCode_(std::move(objectCode))
        , warnings_(std::move(warnings)) {}
    
    // Object code access
    const std::vector<std::byte>& ObjectCode() const noexcept {
        return objectCode_;
    }
    
    std::span<const std::byte> ObjectSpan() const noexcept {
        return objectCode_;
    }
    
    // Warning access
    const std::vector<AssemblyWarning>& Warnings() const noexcept {
        return warnings_;
    }
    
    bool HasWarnings() const noexcept {
        return !warnings_.empty();
    }
    
    // Size information
    std::size_t Size() const noexcept {
        return objectCode_.size();
    }
    
    bool Empty() const noexcept {
        return objectCode_.empty();
    }

private:
    std::vector<std::byte> objectCode_;
    std::vector<AssemblyWarning> warnings_;
};

/*****************************************************************************/
/*                                Result Type                               */
/*****************************************************************************/

using AssemblyResult = std::expected<AssemblyOutput, AssemblyError>;

/*****************************************************************************/
/*                                Concepts                                  */
/*****************************************************************************/

template<typename T>
concept StringLike = std::convertible_to<T, std::string_view>;

template<typename T>
concept AssemblySource = requires(T t) {
    { t.data() } -> std::convertible_to<const char*>;
    { t.size() } -> std::convertible_to<std::size_t>;
};

template<typename T>
concept IntegralValue = std::integral<T>;

} // namespace NExT::Assembler::M6502
