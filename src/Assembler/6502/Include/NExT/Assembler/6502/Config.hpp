/*****************************************************************************/
/*                                                                           */
/*                                 Config.hpp                               */
/*                                                                           */
/*                   Configuration for NExT 6502 Assembler                  */
/*                                                                           */
/*****************************************************************************/

#pragma once

#include "Types.hpp"
#include <vector>
#include <string>
#include <string_view>
#include <ranges>
#include <format>

namespace NExT::Assembler::M6502 {

/*****************************************************************************/
/*                           Assembler Configuration                        */
/*****************************************************************************/

class AssemblerConfig {
public:
    AssemblerConfig() = default;
    
    // CPU configuration
    constexpr AssemblerConfig& Cpu(CpuType type) noexcept {
        cpu_ = type;
        return *this;
    }
    
    // Target system configuration
    constexpr AssemblerConfig& Target(TargetSystem system) noexcept {
        target_ = system;
        return *this;
    }
    
    // Memory model configuration
    constexpr AssemblerConfig& MemoryModel(M6502::MemoryModel model) noexcept {
        memoryModel_ = model;
        return *this;
    }
    
    // Debug information
    constexpr AssemblerConfig& DebugInfo(bool enable = true) noexcept {
        debugInfo_ = enable;
        return *this;
    }
    
    // Smart mode (track REP/SEP instructions on 65816)
    constexpr AssemblerConfig& SmartMode(bool enable = true) noexcept {
        smartMode_ = enable;
        return *this;
    }
    
    // Case sensitivity
    constexpr AssemblerConfig& IgnoreCase(bool enable = true) noexcept {
        ignoreCase_ = enable;
        return *this;
    }
    
    // Auto-import undefined symbols
    constexpr AssemblerConfig& AutoImport(bool enable = true) noexcept {
        autoImport_ = enable;
        return *this;
    }
    
    // Treat warnings as errors
    constexpr AssemblerConfig& WarningsAsErrors(bool enable = true) noexcept {
        warningsAsErrors_ = enable;
        return *this;
    }
    
    // Relax some error checks
    constexpr AssemblerConfig& RelaxChecks(bool enable = true) noexcept {
        relaxChecks_ = enable;
        return *this;
    }
    
    // Add include path
    AssemblerConfig& IncludePath(std::string_view path) {
        includePaths_.emplace_back(path);
        return *this;
    }
    
    // Add multiple include paths
    template<std::ranges::range R>
    AssemblerConfig& IncludePaths(R&& paths) {
        for (auto&& path : paths) {
            includePaths_.emplace_back(path);
        }
        return *this;
    }
    
    // Define symbol with integral value
    template<IntegralValue T>
    AssemblerConfig& Define(std::string_view name, T value) {
        defines_.emplace_back(std::format("{}={}", name, value));
        return *this;
    }
    
    // Define symbol with string value
    AssemblerConfig& Define(std::string_view name, std::string_view value) {
        defines_.emplace_back(std::format("{}={}", name, value));
        return *this;
    }
    
    // Define symbol without value (defaults to 1)
    AssemblerConfig& Define(std::string_view name) {
        defines_.emplace_back(std::format("{}=1", name));
        return *this;
    }
    
    // Getters
    constexpr CpuType Cpu() const noexcept { return cpu_; }
    constexpr TargetSystem Target() const noexcept { return target_; }
    constexpr M6502::MemoryModel MemoryModel() const noexcept { return memoryModel_; }
    constexpr bool DebugInfo() const noexcept { return debugInfo_; }
    constexpr bool SmartMode() const noexcept { return smartMode_; }
    constexpr bool IgnoreCase() const noexcept { return ignoreCase_; }
    constexpr bool AutoImport() const noexcept { return autoImport_; }
    constexpr bool WarningsAsErrors() const noexcept { return warningsAsErrors_; }
    constexpr bool RelaxChecks() const noexcept { return relaxChecks_; }
    
    const std::vector<std::string>& IncludePaths() const noexcept { 
        return includePaths_; 
    }
    
    const std::vector<std::string>& Defines() const noexcept { 
        return defines_; 
    }
    
    // Validation
    bool IsValid() const noexcept;
    std::string ValidationError() const;

private:
    CpuType cpu_{CpuType::MOS6502};
    TargetSystem target_{TargetSystem::None};
    M6502::MemoryModel memoryModel_{M6502::MemoryModel::Near};
    bool debugInfo_{false};
    bool smartMode_{false};
    bool ignoreCase_{false};
    bool autoImport_{false};
    bool warningsAsErrors_{false};
    bool relaxChecks_{false};
    std::vector<std::string> includePaths_;
    std::vector<std::string> defines_;
};

/*****************************************************************************/
/*                              Factory Functions                           */
/*****************************************************************************/

// Create default configuration for specific targets
AssemblerConfig DefaultConfigFor(TargetSystem target);

// Predefined configurations for common systems
AssemblerConfig C64Config();
AssemblerConfig NESConfig();
AssemblerConfig Apple2Config();
AssemblerConfig AtariConfig();
AssemblerConfig Generic6502Config();

/*****************************************************************************/
/*                              Builder Pattern                             */
/*****************************************************************************/

class AssemblerBuilder {
public:
    AssemblerBuilder() = default;
    
    AssemblerBuilder& ForTarget(TargetSystem target) {
        config_.Target(target);
        return *this;
    }
    
    AssemblerBuilder& WithCpu(CpuType cpu) {
        config_.Cpu(cpu);
        return *this;
    }
    
    AssemblerBuilder& WithDebugInfo() {
        config_.DebugInfo(true);
        return *this;
    }
    
    AssemblerBuilder& WithSmartMode() {
        config_.SmartMode(true);
        return *this;
    }
    
    AssemblerBuilder& CaseInsensitive() {
        config_.IgnoreCase(true);
        return *this;
    }
    
    AssemblerBuilder& WithAutoImport() {
        config_.AutoImport(true);
        return *this;
    }
    
    AssemblerBuilder& WarningsAsErrors() {
        config_.WarningsAsErrors(true);
        return *this;
    }
    
    AssemblerBuilder& RelaxChecks() {
        config_.RelaxChecks(true);
        return *this;
    }
    
    AssemblerBuilder& IncludePath(std::string_view path) {
        config_.IncludePath(path);
        return *this;
    }
    
    template<IntegralValue T>
    AssemblerBuilder& Define(std::string_view name, T value) {
        config_.Define(name, value);
        return *this;
    }
    
    AssemblerBuilder& Define(std::string_view name, std::string_view value) {
        config_.Define(name, value);
        return *this;
    }
    
    AssemblerBuilder& Define(std::string_view name) {
        config_.Define(name);
        return *this;
    }
    
    AssemblerConfig Build() const {
        return config_;
    }

private:
    AssemblerConfig config_;
};

} // namespace NExT::Assembler::M6502
