/*****************************************************************************/
/*                                                                           */
/*                              test_ca65lib.c                              */
/*                                                                           */
/*                    Test program for ca65 library interface               */
/*                                                                           */
/*****************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "ca65_lib.h"

/* Test assembly code */
static const char* test_assembly = 
    "; Simple 6502 test program\n"
    ".segment \"CODE\"\n"
    "\n"
    "start:\n"
    "    lda #$00\n"
    "    sta $d020\n"
    "    lda #$01\n"
    "    sta $d021\n"
    "\n"
    "loop:\n"
    "    inc $d020\n"
    "    jmp loop\n"
    "\n"
    ".segment \"VECTORS\"\n"
    ".word start\n"
    ".word start\n"
    ".word start\n";

static void print_result(Ca65Result* result) {
    if (!result) {
        printf("Assembly failed - no result\n");
        return;
    }
    
    printf("Assembly completed:\n");
    printf("  Object size: %zu bytes\n", result->ObjectSize);
    printf("  Errors: %d\n", result->ErrorCount);
    printf("  Warnings: %d\n", result->WarningCount);
    
    if (result->ErrorCount > 0) {
        printf("Errors:\n");
        for (int i = 0; i < result->ErrorCount; i++) {
            const char* error = ca65_result_get_error(result, i);
            if (error) {
                printf("  %d: %s\n", i + 1, error);
            }
        }
    }
    
    if (result->WarningCount > 0) {
        printf("Warnings:\n");
        for (int i = 0; i < result->WarningCount; i++) {
            const char* warning = ca65_result_get_warning(result, i);
            if (warning) {
                printf("  %d: %s\n", i + 1, warning);
            }
        }
    }
    
    if (result->ObjectSize > 0) {
        printf("Object data (first 32 bytes):\n");
        for (size_t i = 0; i < result->ObjectSize && i < 32; i++) {
            printf("%02x ", result->ObjectData[i]);
            if ((i + 1) % 16 == 0) printf("\n");
        }
        if (result->ObjectSize > 0 && result->ObjectSize <= 32) {
            printf("\n");
        } else if (result->ObjectSize > 32) {
            printf("... (%zu more bytes)\n", result->ObjectSize - 32);
        }
    }
}

static void test_basic_assembly(void) {
    printf("=== Test: Basic Assembly ===\n");
    
    /* Create context with default configuration */
    Ca65Context* ctx = ca65_context_new();
    if (!ctx) {
        printf("Failed to create context\n");
        return;
    }
    
    /* Set target to C64 */
    if (!ca65_context_set_target(ctx, CA65_TARGET_C64)) {
        printf("Failed to set target\n");
        ca65_context_free(ctx);
        return;
    }
    
    /* Assemble the test code */
    Ca65Result* result = ca65_assemble_string(ctx, test_assembly);
    print_result(result);
    
    /* Cleanup */
    ca65_result_free(result);
    ca65_context_free(ctx);
    printf("\n");
}

static void test_with_config(void) {
    printf("=== Test: Assembly with Custom Config ===\n");
    
    /* Create custom configuration */
    Ca65Config config = ca65_get_default_config(CA65_TARGET_NES);
    config.CPU = CA65_CPU_6502;
    config.DebugInfo = 1;
    config.SmartMode = 0;
    
    /* Validate configuration */
    if (!ca65_validate_config(&config)) {
        printf("Invalid configuration\n");
        return;
    }
    
    /* Create context with custom config */
    Ca65Context* ctx = ca65_context_new_with_config(&config);
    if (!ctx) {
        printf("Failed to create context with config\n");
        return;
    }
    
    /* Define some symbols */
    ca65_context_define_symbol(ctx, "SCREEN_RAM", 0x0400);
    ca65_context_define_symbol(ctx, "COLOR_RAM", 0xd800);
    
    /* Test assembly with symbols */
    const char* nes_assembly = 
        "; NES test program\n"
        ".segment \"HEADER\"\n"
        ".byte \"NES\", $1a\n"
        ".byte 2, 1, 0, 0\n"
        ".res 8, 0\n"
        "\n"
        ".segment \"CODE\"\n"
        "reset:\n"
        "    sei\n"
        "    cld\n"
        "    ldx #$ff\n"
        "    txs\n"
        "    jmp reset\n";
    
    Ca65Result* result = ca65_assemble_string(ctx, nes_assembly);
    print_result(result);
    
    /* Cleanup */
    ca65_result_free(result);
    ca65_context_free(ctx);
    printf("\n");
}

static void test_error_handling(void) {
    printf("=== Test: Error Handling ===\n");
    
    Ca65Context* ctx = ca65_context_new();
    if (!ctx) {
        printf("Failed to create context\n");
        return;
    }
    
    /* Test assembly with syntax errors */
    const char* bad_assembly = 
        "; Assembly with errors\n"
        ".segment \"CODE\"\n"
        "\n"
        "start:\n"
        "    lda #$100    ; Error: value too large for byte\n"
        "    sta undefined_label  ; Error: undefined symbol\n"
        "    invalid_opcode  ; Error: unknown instruction\n"
        "    lda          ; Error: missing operand\n";
    
    Ca65Result* result = ca65_assemble_string(ctx, bad_assembly);
    print_result(result);
    
    /* Cleanup */
    ca65_result_free(result);
    ca65_context_free(ctx);
    printf("\n");
}

int main(void) {
    printf("ca65 Library Test Program\n");
    printf("========================\n\n");
    
    /* Initialize the library */
    if (!ca65_init()) {
        printf("Failed to initialize ca65 library\n");
        return 1;
    }
    
    /* Run tests */
    test_basic_assembly();
    test_with_config();
    test_error_handling();
    
    /* Cleanup the library */
    ca65_cleanup();
    
    printf("All tests completed.\n");
    return 0;
}
