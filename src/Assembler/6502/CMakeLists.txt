# NExT 6502 Assembler Backend
# Modern C++23 wrapper around ca65

cmake_minimum_required(VERSION 3.25)

# Source files for the 6502 backend
set(NEXT_6502_SOURCES
    Source/Ca65Wrapper.cpp
    Source/Config.cpp
    Source/Assembler.cpp
)

# Headers
set(NEXT_6502_HEADERS
    Include/NExT/Assembler/6502/Types.hpp
    Include/NExT/Assembler/6502/Config.hpp
    Include/NExT/Assembler/6502/Assembler.hpp
    Source/Ca65Wrapper.h
)

# Create the 6502 assembler library
if(NEXT_BUILD_SHARED_LIBS)
    add_library(next_6502_assembler SHARED ${NEXT_6502_SOURCES} ${NEXT_6502_HEADERS})
else()
    add_library(next_6502_assembler STATIC ${NEXT_6502_SOURCES} ${NEXT_6502_HEADERS})
endif()

# Set target properties
set_target_properties(next_6502_assembler PROPERTIES
    OUTPUT_NAME "NExT_6502_Assembler"
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    POSITION_INDEPENDENT_CODE ON
)

# Include directories
target_include_directories(next_6502_assembler
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/Include>
        $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/Source
)

# Link with ca65 core library
target_link_libraries(next_6502_assembler
    PRIVATE
        NExT::ca65
)

# Compiler-specific settings
if(MSVC)
    target_compile_options(next_6502_assembler PRIVATE /W4 /permissive-)
    # Enable C++23 modules
    target_compile_options(next_6502_assembler PRIVATE /experimental:module)
else()
    target_compile_options(next_6502_assembler PRIVATE -Wall -Wextra -Wpedantic)
    # Enable C++23 modules for GCC/Clang
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        target_compile_options(next_6502_assembler PRIVATE -fmodules-ts)
    elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
        target_compile_options(next_6502_assembler PRIVATE -fmodules)
    endif()
endif()

# Create alias for consistent naming
add_library(NExT::Assembler::6502 ALIAS next_6502_assembler)

# Installation
install(TARGETS next_6502_assembler
    EXPORT NExTTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# Install headers
install(DIRECTORY Include/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
    FILES_MATCHING PATTERN "*.hpp"
)

# Add tests if enabled
if(NEXT_BUILD_TESTS)
    add_subdirectory(Tests)
endif()

# Add examples specific to 6502
if(NEXT_BUILD_EXAMPLES)
    # Examples will be added later
endif()
