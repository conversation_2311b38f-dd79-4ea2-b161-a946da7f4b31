/*****************************************************************************/
/*                                                                           */
/*                               TestTypes.cpp                              */
/*                                                                           */
/*                      Unit tests for NExT 6502 types                      */
/*                                                                           */
/*****************************************************************************/

#include <gtest/gtest.h>
#include "NExT/Assembler/6502/Types.hpp"

using namespace NExT::Assembler::M6502;

/*****************************************************************************/
/*                              Type Tests                                  */
/*****************************************************************************/

TEST(TypesTest, CpuTypeEnumValues) {
    // Test that CPU type enum has expected values
    EXPECT_EQ(static_cast<int>(CpuType::MOS6502), 0);
    EXPECT_NE(CpuType::MOS6502, CpuType::WDC65816);
}

TEST(TypesTest, TargetSystemEnumValues) {
    // Test that target system enum has expected values
    EXPECT_EQ(static_cast<int>(TargetSystem::None), 0);
    EXPECT_NE(TargetSystem::Commodore64, TargetSystem::NES);
}

TEST(TypesTest, MemoryModelEnumValues) {
    // Test memory model enum
    EXPECT_EQ(static_cast<int>(MemoryModel::Near), 0);
    EXPECT_NE(MemoryModel::Near, MemoryModel::Far);
}

/*****************************************************************************/
/*                           Assembly Error Tests                           */
/*****************************************************************************/

TEST(AssemblyErrorTest, Construction) {
    AssemblyError error("Test error message");
    EXPECT_EQ(error.Message, "Test error message");
    EXPECT_EQ(error.LineNumber, 0);
    EXPECT_EQ(error.Column, 0);
}

TEST(AssemblyErrorTest, WithLineNumber) {
    AssemblyError error("Test error");
    error.LineNumber = 42;
    error.Column = 10;
    
    EXPECT_EQ(error.Message, "Test error");
    EXPECT_EQ(error.LineNumber, 42);
    EXPECT_EQ(error.Column, 10);
}

/*****************************************************************************/
/*                          Assembly Warning Tests                          */
/*****************************************************************************/

TEST(AssemblyWarningTest, Construction) {
    AssemblyWarning warning("Test warning message");
    EXPECT_EQ(warning.Message, "Test warning message");
    EXPECT_EQ(warning.LineNumber, 0);
    EXPECT_EQ(warning.Column, 0);
}

/*****************************************************************************/
/*                          Assembly Output Tests                           */
/*****************************************************************************/

TEST(AssemblyOutputTest, EmptyOutput) {
    std::vector<std::byte> emptyCode;
    AssemblyOutput output(std::move(emptyCode));
    
    EXPECT_TRUE(output.Empty());
    EXPECT_EQ(output.Size(), 0);
    EXPECT_FALSE(output.HasWarnings());
}

TEST(AssemblyOutputTest, WithObjectCode) {
    std::vector<std::byte> code = {
        std::byte{0xA9}, std::byte{0x00}, // LDA #$00
        std::byte{0x8D}, std::byte{0x20}, std::byte{0xD0} // STA $D020
    };
    
    AssemblyOutput output(std::move(code));
    
    EXPECT_FALSE(output.Empty());
    EXPECT_EQ(output.Size(), 5);
    EXPECT_EQ(output.ObjectCode().size(), 5);
    EXPECT_EQ(output.ObjectSpan().size(), 5);
}

TEST(AssemblyOutputTest, WithWarnings) {
    std::vector<std::byte> code;
    std::vector<AssemblyWarning> warnings;
    warnings.emplace_back("Test warning 1");
    warnings.emplace_back("Test warning 2");
    
    AssemblyOutput output(std::move(code), std::move(warnings));
    
    EXPECT_TRUE(output.HasWarnings());
    EXPECT_EQ(output.Warnings().size(), 2);
    EXPECT_EQ(output.Warnings()[0].Message, "Test warning 1");
    EXPECT_EQ(output.Warnings()[1].Message, "Test warning 2");
}

/*****************************************************************************/
/*                             Concept Tests                                */
/*****************************************************************************/

TEST(ConceptsTest, StringLikeConcept) {
    // Test that string-like types satisfy the concept
    static_assert(StringLike<std::string>);
    static_assert(StringLike<std::string_view>);
    static_assert(StringLike<const char*>);
    static_assert(StringLike<char*>);
    
    // Test that non-string types don't satisfy the concept
    static_assert(!StringLike<int>);
    static_assert(!StringLike<std::vector<char>>);
}

TEST(ConceptsTest, IntegralValueConcept) {
    // Test that integral types satisfy the concept
    static_assert(IntegralValue<int>);
    static_assert(IntegralValue<long>);
    static_assert(IntegralValue<short>);
    static_assert(IntegralValue<unsigned int>);
    static_assert(IntegralValue<std::size_t>);
    
    // Test that non-integral types don't satisfy the concept
    static_assert(!IntegralValue<float>);
    static_assert(!IntegralValue<double>);
    static_assert(!IntegralValue<std::string>);
}
