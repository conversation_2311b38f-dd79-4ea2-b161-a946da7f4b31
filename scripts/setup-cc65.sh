#!/bin/bash

# <PERSON>ript to download and setup cc65 source code for NExT project

set -e  # Exit on any error

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CC65_DIR="$PROJECT_ROOT/3rdparty/cc65"

echo "Setting up cc65 source code for NExT project..."
echo "Project root: $PROJECT_ROOT"
echo "CC65 directory: $CC65_DIR"

# Create 3rdparty directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/3rdparty"

# Check if cc65 directory already exists
if [ -d "$CC65_DIR" ]; then
    echo "CC65 directory already exists. Checking if it's a git repository..."
    
    if [ -d "$CC65_DIR/.git" ]; then
        echo "Updating existing cc65 repository..."
        cd "$CC65_DIR"
        git fetch origin
        git reset --hard origin/master
        echo "CC65 repository updated successfully."
    else
        echo "Warning: CC65 directory exists but is not a git repository."
        echo "Please remove $CC65_DIR and run this script again."
        exit 1
    fi
else
    echo "Cloning cc65 repository..."
    cd "$PROJECT_ROOT/3rdparty"
    git clone https://github.com/cc65/cc65.git
    echo "CC65 repository cloned successfully."
fi

# Verify that required files exist
echo "Verifying cc65 source files..."

REQUIRED_FILES=(
    "src/ca65/main.c"
    "src/ca65/scanner.c"
    "src/ca65/parser.c"
    "src/ca65/macro.c"
    "src/common/target.c"
    "src/common/cpu.c"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$CC65_DIR/$file" ]; then
        echo "Error: Required file $file not found in cc65 source."
        exit 1
    fi
done

echo "All required cc65 source files found."

# Create a version info file
cd "$CC65_DIR"
CC65_VERSION=$(git describe --tags --always --dirty)
CC65_COMMIT=$(git rev-parse HEAD)
CC65_DATE=$(git log -1 --format=%cd --date=iso)

cat > "$PROJECT_ROOT/3rdparty/cc65-version.txt" << EOF
CC65 Version Information
========================
Version: $CC65_VERSION
Commit: $CC65_COMMIT
Date: $CC65_DATE
Repository: https://github.com/cc65/cc65.git

This file was generated automatically by scripts/setup-cc65.sh
EOF

echo "CC65 setup completed successfully!"
echo "Version: $CC65_VERSION"
echo "You can now build the NExT project with CMake."
