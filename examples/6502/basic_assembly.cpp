/*****************************************************************************/
/*                                                                           */
/*                            basic_assembly.cpp                            */
/*                                                                           */
/*                    Basic example of using NExT 6502 Assembler            */
/*                                                                           */
/*****************************************************************************/

#include "NExT/Assembler/6502/Assembler.hpp"
#include <iostream>
#include <format>

using namespace NExT::Assembler::M6502;

int main() {
    std::cout << "NExT 6502 Assembler - Basic Example\n";
    std::cout << "====================================\n\n";
    
    try {
        // Create a basic 6502 assembler
        auto assembler = MakeGeneric6502Assembler();
        
        // Simple 6502 assembly program
        std::string_view source = R"(
            ; Simple 6502 program
            .segment "CODE"
            
            start:
                lda #$00        ; Load accumulator with 0
                ldx #$10        ; Load X register with 16
                ldy #$20        ; Load Y register with 32
                
            loop:
                sta $0200,x     ; Store A at $0200 + X
                inx             ; Increment X
                cmp #$FF        ; Compare with 255
                bne loop        ; Branch if not equal
                
                rts             ; Return from subroutine
        )";
        
        std::cout << "Assembling source code...\n";
        std::cout << "Source:\n" << source << "\n\n";
        
        // Assemble the code
        auto result = assembler.Assemble(source);
        
        if (result) {
            const auto& output = *result;
            
            std::cout << "Assembly successful!\n";
            std::cout << std::format("Generated {} bytes of object code\n", output.Size());
            
            // Display warnings if any
            if (output.HasWarnings()) {
                std::cout << "\nWarnings:\n";
                for (const auto& warning : output.Warnings()) {
                    std::cout << std::format("  Line {}: {}\n", 
                                           warning.LineNumber, warning.Message);
                }
            }
            
            // Display object code (first 32 bytes)
            const auto& objectCode = output.ObjectCode();
            if (!objectCode.empty()) {
                std::cout << "\nObject code (hex dump):\n";
                for (size_t i = 0; i < std::min(objectCode.size(), size_t{32}); ++i) {
                    if (i % 16 == 0) {
                        std::cout << std::format("{:04X}: ", i);
                    }
                    std::cout << std::format("{:02X} ", static_cast<uint8_t>(objectCode[i]));
                    if ((i + 1) % 16 == 0) {
                        std::cout << "\n";
                    }
                }
                if (objectCode.size() % 16 != 0) {
                    std::cout << "\n";
                }
                
                if (objectCode.size() > 32) {
                    std::cout << std::format("... ({} more bytes)\n", objectCode.size() - 32);
                }
            }
            
        } else {
            const auto& error = result.error();
            std::cout << "Assembly failed!\n";
            std::cout << std::format("Error: {}\n", error.Message);
            if (error.LineNumber > 0) {
                std::cout << std::format("Line {}, Column {}\n", 
                                       error.LineNumber, error.Column);
            }
            return 1;
        }
        
    } catch (const std::exception& e) {
        std::cout << std::format("Exception: {}\n", e.what());
        return 1;
    }
    
    std::cout << "\nExample completed successfully!\n";
    return 0;
}
