# NExT CMake configuration file

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Check if we're building with C++23 support
if(CMAKE_CXX_STANDARD LESS 23)
    message(WARNING "NExT requires C++23. Current standard: ${CMAKE_CXX_STANDARD}")
endif()

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/NExTTargets.cmake")

# Provide information about available components
set(NExT_COMPONENTS Assembler::6502)

# Check requested components
foreach(component ${NExT_FIND_COMPONENTS})
    if(NOT component IN_LIST NExT_COMPONENTS)
        set(NExT_FOUND FALSE)
        set(NExT_NOT_FOUND_MESSAGE "Unknown component: ${component}")
        return()
    endif()
endforeach()

# Set found flag
set(NExT_FOUND TRUE)

# Provide version information
set(NExT_VERSION @PROJECT_VERSION@)
set(NExT_VERSION_MAJOR @PROJECT_VERSION_MAJOR@)
set(NExT_VERSION_MINOR @PROJECT_VERSION_MINOR@)
set(NExT_VERSION_PATCH @PROJECT_VERSION_PATCH@)

# Print information
if(NOT NExT_FIND_QUIETLY)
    message(STATUS "Found NExT: ${NExT_VERSION}")
    message(STATUS "  Available components: ${NExT_COMPONENTS}")
endif()
